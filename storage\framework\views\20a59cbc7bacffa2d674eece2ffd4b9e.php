
<?php $__env->startSection('head'); ?>
    <?php echo $__env->make('layouts.main.headersection', [
        'buttons' => [
            [
                'name' => 'Back',
                'url' => route('user.contact.index'),
            ],
        ],
    ], \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
<?php $__env->stopSection(); ?>
<?php $__env->startPush('topcss'); ?>
    <link rel="stylesheet" type="text/css" href="<?php echo e(asset('assets/vendor/select2/dist/css/select2.min.css')); ?>">
<?php $__env->stopPush(); ?>
<?php $__env->startSection('content'); ?>
    <div class="row justify-content-center">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h4><?php echo e(__('Create Contact')); ?></h4>
                </div>
                <div class="card-body">
                    <form method="POST" class="ajaxform_reset_form" action="<?php echo e(route('user.contact.store')); ?>">
                        <?php echo csrf_field(); ?>
                        <div class="form-group row mb-4">
                            <label
                                class="col-form-label text-md-right col-12 col-md-3 col-lg-3"><?php echo e(__('User Name')); ?></label>
                            <div class="col-sm-12 col-md-7">
                                <input type="text" name="name" placeholder="Jhone Doe" maxlength="50"
                                    class="form-control">
                            </div>
                        </div>
                        <div class="form-group row mb-4">
                            <label
                                class="col-form-label text-md-right col-12 col-md-3 col-lg-3"><?php echo e(__('Mobile Number')); ?></label>
                            <div class="col-sm-12 col-md-7">
                                <input type="number" name="phone"
                                    placeholder="<?php echo e(__('Enter Phone Number With Country Code')); ?>" maxlength="15"
                                    class="form-control">
                            </div>
                        </div>
                        <div class="form-group row mb-4">
                            <label
                                class="col-form-label text-md-right col-12 col-md-3 col-lg-3"><?php echo e(__('Select Group')); ?></label>
                            <div class="col-sm-12 col-md-7">
                                <select name="group" class="form-control" data-toggle="select">
                                    <?php $__currentLoopData = $groups; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $group): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                        <option value="<?php echo e($group->id); ?>"><?php echo e($group->name); ?></option>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                </select>
                            </div>
                        </div>
                        <div class="form-group row mb-4">
                            <label
                                class="col-form-label text-md-right col-12 col-md-3 col-lg-3"><?php echo e(__('Birthday Date')); ?></label>
                            <div class="col-sm-12 col-md-7">
                                <input type="date" name="birthday_date" class="form-control">
                            </div>
                        </div>
                        <div class="form-group row mb-4">
                            <label
                                class="col-form-label text-md-right col-12 col-md-3 col-lg-3"><?php echo e(__('Anninversary Date')); ?></label>
                            <div class="col-sm-12 col-md-7">
                                <input type="date" name="anniversary_date" class="form-control">
                            </div>
                        </div>
                        <div class="form-group row mb-4">
                            <label class="col-form-label text-md-right col-12 col-md-3 col-lg-3"></label>
                            <div class="col-sm-12 col-md-7">
                                <button type="submit"
                                    class="btn btn-outline-primary submit-btn"><?php echo e(__('Create Now')); ?></button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
<?php $__env->stopSection(); ?>
<?php $__env->startPush('js'); ?>
    <script src="<?php echo e(asset('assets/vendor/select2/dist/js/select2.min.js')); ?>"></script>
<?php $__env->stopPush(); ?>

<?php echo $__env->make('layouts.main.app', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\Users\<USER>\OneDrive\Vishal\xampp\htdocs\waba_panelv2\resources\views/user/contact/create.blade.php ENDPATH**/ ?>