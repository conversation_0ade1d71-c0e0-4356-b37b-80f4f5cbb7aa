
<?php $__env->startSection('head'); ?>
    <?php echo $__env->make('layouts.main.headersection', [
        'title' => __('Link Device'),
        'buttons' => [
            [
                'name' => __('Back'),
                'url' => route('admin.devices.index'),
            ],
        ],
    ], \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
<?php $__env->stopSection(); ?>
<?php $__env->startSection('content'); ?>
    <div class="row">
        <div class="col-lg-5 mt-5">
            <strong><?php echo e(__('Link Device')); ?></strong>
            <p><?php echo e(__('add device')); ?></p>
        </div>
        <div class="col-lg-7 mt-5">
            <div class="card">
                <form method="post" action="<?php echo e(route('admin.devices.store')); ?>" class="ajaxform_instant_reload"
                    autocomplete="off">
                    <?php echo csrf_field(); ?>
                    <div class="card-body">
                        <div class="pt-20">
                            <div class="form-group">
                                <label for="name">Customer</label>
                                <select class="form-control" name="customer">
                                    <option value=""><?php echo e(__('Select User')); ?></option>
                                    <?php $__currentLoopData = $customers; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $customer): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                        <option value="<?php echo e($customer['id']); ?>">
                                            <?php echo e($customer['name'] . ' - ' . $customer['email']); ?>

                                        </option>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                </select>
                            </div>
                            <div class="form-group">
                                <label for="name">Enter Name</label>
                                <input type="text" placeholder="Enter Name" name="name" class="form-control"
                                    id="name" required>
                            </div>
                            <div class="form-group">
                                <label for="phone">Registered Number with Country Code <small>(optional)</small></label>
                                <input type="text" placeholder="919876543210" name="phone" class="form-control"
                                    id="phone">
                            </div>
                            <div class="form-group">
                                <label for="token">Agent Id</label>
                                <input type="text" placeholder="Your Agent ID" name="token" class="form-control"
                                    id="token">
                            </div>
                        </div>
                    </div>
                    <div class="card-footer">
                        <div class="btn-publish">
                            <button type="submit" class="btn btn-neutral submit-button"><i class="fa fa-save"></i>
                                <?php echo e(__('Save')); ?></button>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    <?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.main.app', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\Users\<USER>\OneDrive\Vishal\xampp\htdocs\waba_panelv2\resources\views/admin/devices/create.blade.php ENDPATH**/ ?>