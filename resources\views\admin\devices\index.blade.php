@extends('layouts.main.app')
@section('head')
    @include('layouts.main.headersection', [
        'title' => __('Devices'),
        'buttons' => [
            [
                'name' => '<i class="fa fa-plus"></i>&nbsp' . __('Manual Link Device'),
                'url' => route('admin.devices.create'),
            ],
        ],
    ])
@endsection
@section('content')
    <div class="row justify-content-center">
        <div class="col-12">
            <div class="row d-flex justify-content-between flex-wrap">
                <div class="col">
                    <div class="card card-stats">
                        <div class="card-body">
                            <div class="row">
                                <div class="col">
                                    <span class="h2 font-weight-bold mb-0 total-transfers" id="total-device">
                                        {{ $totalDevices }}
                                    </span>
                                </div>
                                <div class="col-auto">
                                    <div class="icon icon-shape bg-gradient-primary text-white rounded-circle shadow">
                                        <i class="fi fi-rs-devices mt-2"></i>
                                    </div>
                                </div>
                            </div>
                            <p class="mt-3 mb-0 text-sm">
                            </p>
                            <h5 class="card-title  text-muted mb-0">{{ __('Total Devices') }}</h5>
                            <p></p>
                        </div>
                    </div>
                </div>
                <div class="col">
                    <div class="card card-stats">
                        <div class="card-body">
                            <div class="row">
                                <div class="col">
                                    <span class="h2 font-weight-bold mb-0 total-transfers" id="total-active">
                                        {{ $totalActiveDevices }}
                                    </span>
                                </div>
                                <div class="col-auto">
                                    <div class="icon icon-shape bg-gradient-primary text-white rounded-circle shadow">
                                        <i class="fi fi-rs-badge-check mt-2"></i>
                                    </div>
                                </div>
                            </div>
                            <p class="mt-3 mb-0 text-sm">
                            </p>
                            <h5 class="card-title  text-muted mb-0">{{ __('Active Devices') }}</h5>
                            <p></p>
                        </div>
                    </div>
                </div>
                <div class="col">
                    <div class="card card-stats">
                        <div class="card-body">
                            <div class="row">
                                <div class="col">
                                    <span class="h2 font-weight-bold mb-0 completed-transfers" id="total-inactive">
                                        {{ $totalInactiveDevices }}
                                    </span>
                                </div>
                                <div class="col-auto">
                                    <div class="icon icon-shape bg-gradient-primary text-white rounded-circle shadow">
                                        <i class="fi  fi-rs-exclamation mt-2"></i>
                                    </div>
                                </div>
                            </div>
                            <p class="mt-3 mb-0 text-sm">
                            </p>
                            <h5 class="card-title  text-muted mb-0">{{ __('Inactive Devices') }}</h5>
                            <p></p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>


    <div class="row">
        <div class="col">
            <div class="card">
                <!-- Card header -->
                <div class="card-header border-0">
                    <h3 class="mb-0">{{ __('Devices') }}</h3>
                    <form action="" class="card-header-form">
                        <div class="input-group">
                            <input type="text" name="search" value="{{ $request->search ?? '' }}" class="form-control"
                                placeholder="Search......">
                            <select class="form-control" name="type">
                                <option value="email" @if ($type == 'email') selected="" @endif>
                                    {{ __('User Email') }}</option>
                                <option value="name" @if ($type == 'name') selected="" @endif>
                                    {{ __('Device Name') }}</option>
                                {{-- <option value="uuid" @if ($type == 'uuid') selected="" @endif>
                                    {{ __('Device Id') }}</option> --}}
                                <option value="phone" @if ($type == 'phone') selected="" @endif>
                                    {{ __('Phone Number') }}</option>

                            </select>
                            <div class="input-group-btn">
                                <button class="btn btn-neutral btn-icon"><i class="fas fa-search"></i></button>
                            </div>
                        </div>
                    </form>
                </div>
                <!-- Light table -->
                <div class="table-responsive">
                    <table class="table align-items-center table-flush">
                        <thead class="thead-light">
                            <tr>
                                <th class="col-2">{{ __('Device Name') }}</th>
                                <th class="col-4">{{ __('User') }}</th>
                                <th class="col-2">{{ __('Phone') }}</th>
                                <th class="col-1">{{ __('Transactions') }}</th>
                                <th class="col-1">{{ __('Status') }}</th>
                                <th class="col-1 text-left">{{ __('Created At') }}</th>
                                <th class="col-1 text-left">{{ __('Action') }}</th>
                            </tr>
                        </thead>
                        @if (count($devices) != 0)
                            <tbody class="list">
                                @foreach ($devices ?? [] as $device)
                                    <tr>
                                        <td class="text-left">
                                            {{ $device->name }}
                                        </td>
                                        <td>
                                            <a class="text-dark"
                                                href="{{ route('admin.customer.show', $device->user_id) }}">
                                                {{ Str::limit($device->user->name ?? '', 15) }}
                                                <br>
                                                <small>({{ $device->user->email ?? '' }})</small>
                                            </a>
                                        </td>
                                        <td>
                                            {{ $device->phone ?? '' }}
                                        </td>

                                        <td class="text-center">
                                            {{ number_format($device->smstransaction_count) }}
                                        </td>

                                        <td>
                                            <span class="badge badge-{{ $device->status == 1 ? 'success' : 'danger' }}">
                                                {{ $device->status == 1 ? 'Active' : 'Inactive' }}
                                            </span>
                                        </td>

                                        <td class="text-center">
                                            {{ \Carbon\Carbon::parse($device->created_at)->format('d-F-Y') }}
                                        </td>
                                        <td>

                                            <div class="dropdown">
                                                <a class="btn btn-sm btn-icon-only text-light" href="#" role="button"
                                                    data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                                                    <i class="fas fa-ellipsis-v"></i>
                                                </a>
                                                <div class="dropdown-menu dropdown-menu-right dropdown-menu-arrow">
                                                    {{-- <a class="dropdown-item edit-aboutUs" href="#"
                                                        data-device="{{ $device->id }}" data-toggle="modal"
                                                        data-target="#updateAbout">{{ __('Update About Us') }}</a> --}}
                                                    <a class="dropdown-item"
                                                        href="{{ route('admin.devices.edit', $device->id) }}"><i
                                                            class="fa fa-edit"
                                                            aria-hidden="true"></i>{{ __('Edit') }}</a>

                                                    <a class="dropdown-item delete-confirm" href="#"
                                                        data-action="{{ route('admin.devices.destroy', $device->id) }}"><i
                                                            class="fa fa-trash"
                                                            aria-hidden="true"></i>{{ __('Remove') }}</a>
                                                </div>
                                            </div>
                                        </td>
                                    </tr>
                                @endforeach
                            </tbody>
                        @endif
                    </table>
                    @if (count($devices) == 0)
                        <div class="text-center mt-2">
                            <div class="alert  bg-gradient-primary text-white">
                                <span class="text-left">{{ __('!Opps no records found') }}</span>
                            </div>
                        </div>
                    @endif
                </div>
                <div class="card-footer py-4">
                    {{ $devices->appends($request->all())->links('vendor.pagination.bootstrap-4') }}
                </div>
            </div>
        </div>
    </div>
    <div class="modal fade" id="updateAbout" tabindex="-1" aria-labelledby="exampleModalLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <form method="POST" action="{{ route('admin.updateAboutUs') }}" class="ajaxform_instant_reload">
                    @csrf
                    <div class="modal-header">
                        <h3>{{ __('Update About Us') }}</h3>
                    </div>
                    <div class="modal-body">
                        {{-- <div class="form-group"> --}}
                        {{-- <label>{{ __('Device') }}</label> --}}
                        <input type="hidden" id="device" name="device" class="form-control">
                        {{-- </div> --}}
                        <div class="form-group">
                            <label>{{ __('About') }}</label>
                            <textarea class="form-control" required="" name="about">Business Whatsapp Solution By NXC Controls</textarea>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="submit" class="btn btn-outline-primary col-12">{{ __('Update') }}</button>
                    </div>
                </form>
            </div>
        </div>
    </div>
@endsection
@push('js')
    <script>
        $(document).ready(function() {
            // console.log('helloe');

            $('.edit-aboutUs').on('click', function() {
                // console.log('click');

                const device = $(this).data('device')
                // console.log('d', device);

                $('#device').val(device)
            });
        });
    </script>
@endpush
