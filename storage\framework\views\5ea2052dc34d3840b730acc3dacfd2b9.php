
<?php $__env->startSection('head'); ?>
    <?php echo $__env->make('layouts.main.headersection', [
        'title' => __('Facebook Leads'),
    ], \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
<?php $__env->stopSection(); ?>
<?php $__env->startPush('topcss'); ?>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/uikit@3.9.4/dist/css/uikit.min.css">
    <link rel="stylesheet" type="text/css" href="<?php echo e(asset('assets/vendor/select2/dist/css/select2.min.css')); ?>">
<?php $__env->stopPush(); ?>
<?php $__env->startSection('content'); ?>
    <div class="row justify-content-center">
        <div class="col-12">
            <?php if(api_plan() && api_plan()->title == 'Api'): ?>
                <div class="row">
                    <div class="col-sm-12">
                        <div class="alert bg-gradient-primary text-white alert-dismissible fade show" role="alert">
                            <span class="alert-icon"><i class="fi  fi-rs-info text-white"></i></span>
                            <span class="alert-text">
                                <strong><?php echo e(__('!Opps ')); ?></strong>
                                <?php echo e(__('Facebook integration features is not available in your subscription plan')); ?>

                            </span>
                        </div>
                    </div>
                </div>
            <?php endif; ?>
            <div class="row">
                <div class="col-xl-4 col-md-6">
                    <div class="card  border-0">
                        <!-- Card body -->
                        <div class="card-body">
                            <div class="row">
                                <div class="col">
                                    <h5 class="card-title text-uppercase text-muted mb-0 text-dark">
                                        Facebook Integration
                                    </h5>
                                    
                                </div>
                            </div>
                            <p class="mt-3 mb-0 text-sm">
                                <a href="<?php echo e(route('user.fbLeadIntList')); ?>" class="float-right"
                                    style="color: #657bf8; font-weight: bold;"><button
                                        class="btn btn-outline-primary btn-sm"><i
                                            class="fi-rs-arrow-right"></i></button></a>
                            </p>
                        </div>
                    </div>
                </div>
                
            </div>
        </div>
    </div>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.main.app', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\Users\<USER>\OneDrive\Vishal\xampp\htdocs\waba_panelv2\resources\views/user/facebook_leads/index.blade.php ENDPATH**/ ?>