
<?php $__env->startSection('head'); ?>
    <?php echo $__env->make('layouts.main.headersection', [
        'title' => __('Drip Campaign'),
        'buttons' => array_filter([
            [
                'name' => '<i class="fi fi-rs-paper-plane"></i> &nbspSend Drip',
                'url' => '#',
                'components' => 'data-toggle="modal" data-target="#sendDripCampaign"',
                'is_button' => true,
            ],
            [
                'name' => '<i class="fa fa-plus"></i>&nbsp' . __('Create Drip Campaign'),
                'url' => route('user.drip_campaign.create'),
            ],
        ]),
    ], \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
<?php $__env->stopSection(); ?>
<?php $__env->startPush('topcss'); ?>
    
    <link rel="stylesheet" type="text/css" href="<?php echo e(asset('assets/vendor/select2/dist/css/select2.min.css')); ?>">
<?php $__env->stopPush(); ?>
<?php $__env->startSection('content'); ?>
    <div class="row justify-content-center">
        
    </div>
    <div class="row">
        <div class="col">
            <div class="card">
                <!-- Card header -->
                <div class="card-header border-0">
                    <h3 class="mb-0"><?php echo e(__('Drip Campaign List')); ?></h3>
                    <form action="" class="card-header-form">
                        <div class="input-group">
                            <input type="text" name="search" value="<?php echo e($request->search ?? ''); ?>" class="form-control"
                                placeholder="Search......">
                            <select class="form-control" name="type">
                                <option value="email" <?php if($type == 'email'): ?> selected="" <?php endif; ?>>
                                    <?php echo e(__('User Email')); ?></option>
                                <option value="name" <?php if($type == 'name'): ?> selected="" <?php endif; ?>>
                                    <?php echo e(__('Name')); ?></option>

                            </select>
                            <div class="input-group-btn">
                                <button class="btn btn-neutral btn-icon"><i class="fas fa-search"></i></button>
                            </div>
                        </div>
                    </form>
                </div>
                <!-- Light table -->
                <div class="table-responsive">
                    <table class="table align-items-center table-flush">
                        <thead class="thead-light">
                            <tr>
                                <th class="col-1"><?php echo e(__('Drip Name')); ?></th>
                                <th class="col-2"><?php echo e(__('Device')); ?></th>
                                <th class="col-1 text-left"><?php echo e(__('Action')); ?></th>
                            </tr>
                        </thead>
                        <?php if(count($drips) != 0): ?>
                            <tbody class="list">
                                <?php $__currentLoopData = $drips ?? []; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $drip): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <tr>
                                        <td><?php echo e($drip->drip_name ?? ''); ?></td>
                                        <td>
                                            <?php echo e($drip->device->name ?? ''); ?> <br>
                                            <small>(<?php echo e($drip->device->phone); ?>)</small>
                                        </td>

                                        <td>
                                            <div class="dropdown">
                                                <a class="btn btn-sm btn-icon-only text-light" href="#" role="button"
                                                    data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                                                    <i class="fas fa-ellipsis-v"></i>
                                                </a>
                                                <div class="dropdown-menu dropdown-menu-right dropdown-menu-arrow">
                                                    <a class="dropdown-item"
                                                        href="<?php echo e(route('user.drip_campaign.edit', $drip->id)); ?>"><i
                                                            class="ni ni-align-left-2"></i><?php echo e(__('Edit')); ?></a>
                                                    <a class="dropdown-item delete-confirm" href="javascript:void(0)"
                                                        data-action="<?php echo e(route('user.drip_campaign.destroy', $drip->id)); ?>"
                                                        data-text="Are you sure you want to remove this campaign? Messages that have already been sent cannot be deleted"
                                                        data-icon="warning"><i
                                                            class="fas fa-trash"></i><?php echo e(__('Remove')); ?></a>
                                                </div>
                                            </div>
                                        </td>
                                    </tr>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </tbody>
                        <?php endif; ?>
                    </table>
                    <?php if(count($drips) == 0): ?>
                        <div class="text-center mt-2">
                            <div class="alert  bg-gradient-primary text-white">
                                <span class="text-left"><?php echo e(__('!Opps no records found')); ?></span>
                            </div>
                        </div>
                    <?php endif; ?>
                </div>
                
            </div>
        </div>
    </div>

    <div class="modal fade" id="sendDripCampaign" tabindex="-1" aria-labelledby="editModal" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <form type="POST" action="<?php echo e(route('user.drip_campaign.send_drip')); ?>" class="ajaxform_instant_reload">
                    <?php echo csrf_field(); ?>
                    <div class="modal-header">
                        <h5 class="modal-title" id="exampleModalLabel"><?php echo e(__('Send Drip Campaign')); ?></h5>
                        <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                            <span aria-hidden="true">&times;</span>
                        </button>
                    </div>
                    <div class="modal-body">
                        <div class="form-group">
                            <label><?php echo e(__('Select Device')); ?></label>
                            <select class="form-control" name="drip_device" id="drip_device" data-toggle="select" required>
                                <option value=""><?php echo e(__('Select Device')); ?></option>
                                <?php $__currentLoopData = $device_data; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $device): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <option value="<?php echo e($device['id']); ?>"><?php echo e($device['name']); ?> (+<?php echo e($device['phone']); ?>)
                                    </option>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </select>
                        </div>
                        <div class="form-group">
                            <label><?php echo e(__('Drip Name')); ?></label>
                            <select class="form-control" id="drip_name" name="drip_name" data-toggle="select">
                                
                            </select>
                        </div>
                        <div class="form-group">
                            <label><?php echo e(__('WhatsApp Numbers')); ?></label>
                            <textarea name="campaign_numbers[]" class="form-control" placeholder="<?php echo e(__('Enter phone number with country code')); ?>"
                                id="campaign_numbers" oninput="cleanInput()" cols="70" rows="2"></textarea>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="submit"
                            class="btn btn-neutral submit-btn float-right"><?php echo e(__('Create Now')); ?></button>
                    </div>
                </form>
            </div>
        </div>
    </div>


<?php $__env->stopSection(); ?>
<?php $__env->startPush('js'); ?>
    <script src="<?php echo e(asset('assets/vendor/select2/dist/js/select2.min.js')); ?>"></script>
<?php $__env->stopPush(); ?>
<?php $__env->startPush('js'); ?>
    <script>
        function cleanInput() {
            const textarea = document.getElementById('campaign_numbers');
            // Remove whitespace, special characters, and alphabets
            textarea.value = textarea.value.replace(/[^\d\n]/g, '');
        }
    </script>

    <script>
        $(document).ready(function() {
            // document.getElementById('drip_template').innerText = null;

            $(document).ready(function() {
                function selectSingleDevice() {
                    var deviceDropdown = $("#drip_device");
                    if (deviceDropdown.children("option").length == 2) { // One option is the placeholder
                        deviceDropdown.children("option").eq(1).prop('selected', true);
                        deviceDropdown.change();
                    }
                }
                selectSingleDevice();
            });

            $('#drip_device').on('change', function() {
                var device_id = $(this).val();
                var drip_name = $('#drip_name');
                drip_name.empty();
                $.ajax({
                    url: "<?php echo e(route('user.drip_campaign.get_drip_name')); ?>",
                    type: 'POST',
                    data: {
                        device_id: device_id,
                        _token: "<?php echo e(csrf_token()); ?>"
                    },
                    success: function(data) {
                        drip_name.append(
                            '<option value="">Select Drip Name</option>');
                        $.each(data.drip_data, function(key, value) {
                            drip_name.append('<option value="' + value.id + '">' +
                                value.drip_name + '</option>');
                        });
                    }
                });
            });
        });
    </script>
<?php $__env->stopPush(); ?>

<?php echo $__env->make('layouts.main.app', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\Users\<USER>\OneDrive\Vishal\xampp\htdocs\waba_panelv2\resources\views/user/drip_campaign/index.blade.php ENDPATH**/ ?>