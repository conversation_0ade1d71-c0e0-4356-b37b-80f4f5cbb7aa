
<?php $__env->startSection('head'); ?>
    <?php echo $__env->make('layouts.main.headersection', [
        'title' => 'Delete Chat',
    ], \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
<?php $__env->stopSection(); ?>
<?php $__env->startPush('topcss'); ?>
    <link rel="stylesheet" type="text/css" href="<?php echo e(asset('assets/vendor/select2/dist/css/select2.min.css')); ?>">
<?php $__env->stopPush(); ?>
<?php $__env->startSection('content'); ?>
    <div class="row">
        <div class="col-md-11">
            <?php if(Session::has('error')): ?>
                <div class="alert bg-gradient-danger text-white alert-dismissible fade show success-alert" id="success-alert"
                    role="alert">
                    <span class="alert-text"><?php echo e(Session::get('error')); ?></span>
                    <button type="button" class="close" data-dismiss="alert" aria-label="Close">
                        <span aria-hidden="true">×</span>
                    </button>
                </div>
            <?php elseif(Session::has('success')): ?>
                <div class="alert bg-gradient-success text-white alert-dismissible fade show success-alert" id="error-alert"
                    role="alert">
                    <span class="alert-text"><?php echo e(Session::get('success')); ?></span>
                    <button type="button" class="close" data-dismiss="alert" aria-label="Close">
                        <span aria-hidden="true">×</span>
                    </button>
                </div>
            <?php endif; ?>
            <div class="card">
                <div class="card-body">
                    <form id="delete_device_chat" class="add_form">
                        <?php echo csrf_field(); ?>
                        <div class="form-group row">
                            <label class="col-sm-3 col-form-label"><?php echo e(__('Device Number')); ?></label>
                            <div class="col-sm-6">
                                <select class="form-control select2" id="device_number" name="device_number" required="">
                                    <option value=""><?php echo e(__('Select Device')); ?></option>
                                    <?php $__currentLoopData = $devices; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $device): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                        <option value="<?php echo e($device['id']); ?>" data-id="<?php echo e($device['id']); ?>">
                                            <?php echo e($device['name']); ?>

                                            <?php if(!empty($device['phone'])): ?>
                                                (+<?php echo e($device['phone']); ?>)
                                            <?php endif; ?>
                                        </option>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                </select>
                            </div>
                            <div class="col-sm-3">
                                <button type="button" class="btn btn-outline-danger float-right"
                                    onclick="deletePendingMessage(event)"><?php echo e(__('Delete Pending Message')); ?></button>
                            </div>
                        </div>

                        <!-- Hidden input to store device Id -->
                        <input type="hidden" id="device_id" name="device_id">

                        <div class="form-group row">
                            <label class="col-sm-3 col-form-label"><?php echo e(__('Select Days')); ?></label>
                            <div class="col-sm-9">
                                <select class="form-control select2" id="deletechat_daywise" name="deletechat_daywise"
                                    required="">
                                    <option value=""><?php echo e(__('Select Day')); ?></option>
                                    <option value="1" selected>Today</option>
                                    <option value="2">Yesterday</option>
                                    <option value="7">Last 7 Days</option>
                                    <option value="15">Last 15 Days</option>
                                    <option value="30">Last 1 Month</option>
                                    <option value="180">Last 6 Month</option>
                                    <option value="older_than_180">Older Than 6 Month</option>
                                    <option value="all">All</option>
                                </select>
                            </div>
                        </div>
                        <div class="form-group row">
                            <label class="col-sm-3 col-form-label"><?php echo e(__('Select Type')); ?></label>
                            <div class="col-sm-9">
                                <select class="form-control select2" id="chat_received_sent" name="chat_received_sent"
                                    required="">
                                    <option value=""><?php echo e(__('Select Send Or Received')); ?></option>
                                    <option value="send">Send</option>
                                    <option value="received">Received</option>
                                    <option value="all" selected>All</option>
                                </select>
                            </div>
                        </div>
                        <div class="text-center">
                            <button class="btn btn-outline-primary submit-button float-right" type="submit"
                                id="submit_btn"><?php echo e(__('Delete Chat')); ?></button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
<?php $__env->stopSection(); ?>
<?php $__env->startPush('js'); ?>
    <script src="<?php echo e(asset('assets/vendor/select2/dist/js/select2.min.js')); ?>"></script>
    <script src="<?php echo e(asset('assets/js/pages/user/chatbot.js')); ?>"></script>
<?php $__env->stopPush(); ?>

<?php $__env->startPush('js'); ?>
    <script>
        setTimeout(function() {
            document.getElementById('success-alert')?.remove();
            document.getElementById('error-alert')?.remove();
        }, 15000);
    </script>
    <script>
        $(document).ready(function() {
            function selectSingleDevice() {
                var deviceDropdown = $("#device_number");
                if (deviceDropdown.children("option").length == 2) { // One option is the placeholder
                    deviceDropdown.children("option").eq(1).prop('selected', true);
                    deviceDropdown.change();
                }
            }

            $("#device_number").change(function() {
                var selectedDevice = document.getElementById('device_number').selectedOptions[0];
                var deviceId = selectedDevice ? selectedDevice.getAttribute('data-id') : null;

                if (!deviceId) {
                    document.getElementById('device_id').value = null;
                    //alert('Please select a device to delete the pending message.');
                    return;
                }

                // Set the hidden input value to the selected device's UUID
                document.getElementById('device_id').value = deviceId;
            });
            selectSingleDevice();
        });

        $(document).ready(function() {
            $('#submit_btn').on('click', function(e) {
                e.preventDefault();

                $(this).prop('disabled', true);

                var formData = {
                    device_number: $('#device_number').val(),
                    deletechat_daywise: $('#deletechat_daywise').val(),
                    chat_received_sent: $('#chat_received_sent').val(),
                };

                $.ajax({
                    url: "<?php echo e(route('user.deleteChatHistory')); ?>",
                    type: 'POST',
                    data: formData,
                    dataType: 'JSON',
                    headers: {
                        'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content'),
                    },
                    success: function(response) {
                        try {
                            ToastAlert('success', response.message);
                            setTimeout(function() {
                                window.location.href =
                                    "<?php echo e(route('user.deletechat.index')); ?>";
                            }, 2000);
                        } catch (error) {
                            console.error('Error in success callback:', error);
                        }
                    },
                    error: function(xhr, status, error) {
                        console.error('AJAX Request Error:', xhr.responseText);
                        alert('An error occurred: ' + xhr.responseText);
                    },
                    complete: function() {
                        $('#submit_btn').prop('disabled', false);
                    }
                });
            });
        });

        function deletePendingMessage(e) {
            e.preventDefault();

            deviceIds = document.getElementById('device_id').value;

            if (!deviceIds) {
                alert('Please select a device to delete the pending message.');
                return;
            }

            var formData = {
                device_id: deviceIds,
            };

            $.ajax({
                url: "<?php echo e(route('user.deletePendingMsg')); ?>",
                type: 'POST',
                data: formData,
                dataType: 'JSON',
                headers: {
                    'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content'),
                },
                success: function(response) {
                    try {
                        ToastAlert('success', response.message);
                        setTimeout(function() {
                            window.location.href =
                                "<?php echo e(route('user.deletechat.index')); ?>";
                        }, 2000);
                    } catch (error) {
                        console.error('Error in success callback:', error);
                    }
                },
                error: function(xhr, status, error) {
                    console.error('AJAX Request Error:', xhr.responseText);
                    alert('An error occurred: ' + xhr.responseText);
                },
                complete: function() {
                    $('#submit_btn').prop('disabled', false);
                }
            });
        }
    </script>
<?php $__env->stopPush(); ?>

<?php echo $__env->make('layouts.main.app', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\Users\<USER>\OneDrive\Vishal\xampp\htdocs\waba_panelv2\resources\views/user/deletechat/index.blade.php ENDPATH**/ ?>