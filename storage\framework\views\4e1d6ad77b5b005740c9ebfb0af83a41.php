
<?php $__env->startSection('head'); ?>
    <?php echo $__env->make('layouts.main.headersection', [
        'title' => __('Single Send'),
        'buttons' => isset($videoTutorial->value)
            ? [
                [
                    'name' => '<i class="fi-rs-photo-video"></i>&nbsp;&nbsp;' . __('Video Tutorial'),
                    'url' => '#',
                    'components' => 'data-toggle="modal" data-target="#videoModal"',
                    'is_button' => true,
                ],
            ]
            : [],
    ], \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
<?php $__env->stopSection(); ?>
<?php $__env->startPush('topcss'); ?>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/uikit@3.9.4/dist/css/uikit.min.css">
    <link rel="stylesheet" type="text/css" href="<?php echo e(asset('assets/vendor/select2/dist/css/select2.min.css')); ?>">
<?php $__env->stopPush(); ?>
<?php $__env->startSection('content'); ?>
    <div class="row">
        <div class="col-md-8">
            <?php if(api_plan() && api_plan()->title == 'Api'): ?>
                <div class="row">
                    <div class="col-sm-12">
                        <div class="alert bg-gradient-primary text-white alert-dismissible fade show" role="alert">
                            <span class="alert-icon"><i class="fi  fi-rs-info text-white"></i></span>
                            <span class="alert-text">
                                <strong><?php echo e(__('!Opps ')); ?></strong>

                                <?php echo e(__('Send dynamic message features is not available in your subscription plan')); ?>


                            </span>
                        </div>
                    </div>
                </div>
            <?php endif; ?>
            <?php if(Session::has('error')): ?>
                <div class="alert bg-gradient-danger text-white alert-dismissible fade show success-alert"
                    id="success-alert" role="alert">
                    <span class="alert-text"><?php echo e(Session::get('error')); ?></span>
                    <button type="button" class="close" data-dismiss="alert" aria-label="Close">
                        <span aria-hidden="true">×</span>
                    </button>
                </div>
            <?php elseif(Session::has('success')): ?>
                <div class="alert bg-gradient-success text-white alert-dismissible fade show success-alert" id="error-alert"
                    role="alert">
                    <span class="alert-text"><?php echo e(Session::get('success')); ?></span>
                    <button type="button" class="close" data-dismiss="alert" aria-label="Close">
                        <span aria-hidden="true">×</span>
                    </button>
                </div>
            <?php endif; ?>

            <!-- Modal for YouTube video -->
            <div class="modal fade" id="videoModal" tabindex="-1" role="dialog" aria-labelledby="videoModalLabel"
                aria-hidden="true">
                <div class="modal-dialog modal-dialog-centered" role="document">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title" id="videoModalLabel"><?php echo e($videoTutorial->title ?? ''); ?></h5>
                            <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                                <span aria-hidden="true">&times;</span>
                            </button>
                        </div>
                        <div class="modal-body">
                            <iframe id="youtube-video" width="100%" height="315" src="" frameborder="0"
                                allowfullscreen></iframe>
                        </div>
                    </div>
                </div>
            </div>

            <div class="card">
                <div class="card-body">
                    <form id="add_camp_history_second" class="add_form" action="<?php echo e(route('user.sent.DynamicCustomText')); ?>"
                        method="POST" enctype="multipart/form-data">
                        <?php echo csrf_field(); ?>
                        <div class="form-group row">
                            <label class="col-sm-3 col-form-label"><?php echo e(__('Select Device')); ?></label>
                            <div class="col-sm-9">
                                <select class="form-control campaign_device" id="campaign_device" name="campaign_device"
                                    required="" data-toggle="select">
                                    <option value="-1"><?php echo e(__('-- Select Device --')); ?></option>
                                    <?php $__currentLoopData = $device_data; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $device): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                        <option value="<?php echo e($device['id']); ?>"><?php echo e($device['name']); ?>

                                            (+<?php echo e($device['phone']); ?>)
                                        </option>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                </select>
                            </div>
                        </div>
                        <div class="form-group row">
                            <label class="col-sm-3 col-form-label"><?php echo e(__('Select Template')); ?></label>
                            <div class="col-sm-9">
                                <select class="form-control template_select" id="template_select" name="template_select"
                                    required="" data-toggle="select">
                                    <option value="-1"><?php echo e(__('-- Select Template --')); ?></option>

                                </select>
                            </div>
                        </div>
                        <div class="form-group row">
                            <label class="col-sm-3 col-form-label"><?php echo e(__('Campaign Name')); ?></label>
                            <div class="col-sm-9">
                                <input type="text" class="form-control" name="campaign_name" id="campaign_name"
                                    placeholder="Campaign Name">
                            </div>
                        </div>
                        <div class="form-group row">
                            <label class="col-sm-3 col-form-label"><?php echo e(__('Schedule on')); ?></label>
                            <div class="col-sm-9">
                                <input type="datetime-local" name="schedule_on" id="schedule_on" class="form-control"
                                    placeholder="Please choose date & time...">
                            </div>
                        </div>
                        <div class="form-group row" id="temp_langue" style="display: none;">
                            <label class="col-sm-3 col-form-label"></label>
                            <div class="col-sm-9">
                                <input type="text" class="form-control" id="language" name="language"
                                    placeholder="language">
                            </div>
                        </div>
                        <div class="form-group row" id="tsk_type" style="display: none;">
                            <label class="col-sm-3 col-form-label"></label>
                            <div class="col-sm-9">
                                <input type="text" class="form-control" placeholder="task_type" id="task_type"
                                    name="task_type">
                            </div>
                        </div>
                        <div class="form-group row" id="msg_media" style="display: none;">
                            <label class="col-sm-3 col-form-label"><?php echo e(__('Media')); ?> <small
                                    class="text-danger"><?php echo e(__('(Max Size 5 MB, Max Width 1024, MAX Height 768)')); ?></small></label>
                            <div class="col-sm-9">
                                <input type="file" class="form-control" name="media_file" id="media_file" disabled>
                            </div>
                        </div>
                        <div class="form-group row" id="check_media" style="display: none;">
                            <label class="col-sm-3 col-form-label"></label>
                            <div class="col-sm-9">
                                <input type="textarea" id="has_media" name="has_media" class="form-control"
                                    placeholder="has_media">
                            </div>
                        </div>
                        <div class="form-group row" id="contact_media">
                            <label class="col-sm-3 col-form-label"><?php echo e(__('Excel File')); ?> <small
                                    class="text-danger"><?php echo e(__('(Max Size 10 MB)')); ?></small></label>
                            <div class="col-sm-9">
                                <input type="file" class="form-control" name="contact_file" id="contact_file">
                                <a href="<?php echo e(asset('assets/img/format.xlsx')); ?>"
                                    class="btn btn-outline-primary btn-sm mt-2 ml-1"
                                    download="format.xlsx"><?php echo e(__('File Format')); ?></a>
                            </div>
                        </div>

                        <div class="text-center">
                            <button class="btn btn-outline-primary submit-button float-right" type="submit"
                                id="submit_btn"><?php echo e(__('Send Message')); ?></button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
        <div class="card"
            style="width: 18rem; background-image: url('<?php echo e(asset('assets/img/whatsappback.jpg')); ?>'); background-size: cover;">
            <img class="card-img-top mt-2" id="head_image" name="head_image" alt="Card image cap"
                style="border-radius: 20px;padding: 15px;">
            <div class="card-body position-relative">
                <p class="card-title text-primary" id="head_text" name="head_text"></p>
                <p class="card-text" id="campaign_text" name="campaign_text" style="margin-bottom: 5px;"></p>
                <div id="temp_buttons"></div>
            </div>
        </div>
    </div>
<?php $__env->stopSection(); ?>
<?php $__env->startPush('js'); ?>
    <script src="<?php echo e(asset('assets/vendor/select2/dist/js/select2.min.js')); ?>"></script>
    <script src="https://woody180.github.io/vanilla-javascript-emoji-picker/vanillaEmojiPicker.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/uikit@3.9.4/dist/js/uikit.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/uikit@3.9.4/dist/js/uikit-icons.min.js"></script>
    <script src="<?php echo e(asset('assets/js/pages/user/schedule-create.js')); ?>"></script>

    <script type="text/javascript" src="<?php echo e(asset('assets/js/pages/bulk/template.js')); ?>"></script>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Get the current date and time
            const now = new Date();

            // Format the current date and time in 'YYYY-MM-DDTHH:mm' format
            const formattedDate = now.toISOString().slice(0, 16); // 'YYYY-MM-DDTHH:mm'

            // Disable past dates and times by setting the 'min' attribute to current date and time
            const scheduleInput = document.getElementById('schedule_on');
            scheduleInput.setAttribute('min', formattedDate);

            // Disable past dates
            scheduleInput.addEventListener('input', function() {
                if (new Date(scheduleInput.value) < now) {
                    scheduleInput.setCustomValidity('Please select a future date or time');
                } else {
                    scheduleInput.setCustomValidity('');
                }
            });
        });
    </script>

    
    <script>
        setTimeout(function() {
            document.getElementById('success-alert')?.remove();
            document.getElementById('error-alert')?.remove();
        }, 10000);
    </script>
    
    <script>
        //function for formatted text like whatsapp
        function formatWhatsAppTextToHTML(text) {

            function escapeHTML(text) {
                const replacements = {
                    '&': '&amp;',
                    '<': '&lt;',
                    '>': '&gt;',
                    '"': '&quot;',
                    "'": '&#39;'
                };
                return text.replace(/[&<>"']/g, char => replacements[char]);
            }

            // Replace newline characters with <br> tags
            text = text.replace(/\n/g, '<br>');

            // Convert asterisks to <b> tags for bold text
            text = text.replace(/\*(.*?)\*/g, '<b>$1</b>');

            // Convert underscores to <i> tags for italic text
            text = text.replace(/_(.*?)_/g, '<i>$1</i>');

            // Convert backticks to <code> tags for monospace text
            text = text.replace(/```(.*?)```/g, '<code>$1</code>');

            // Convert tildes to <strike> tags for strike-through text
            text = text.replace(/~~(.*?)~~/g, '<strike>$1</strike>');

            return text;
        }

        $(document).ready(function() {
            $(document).ready(function() {
                function selectSingleDevice() {
                    var deviceDropdown = $("#campaign_device");
                    if (deviceDropdown.children("option").length == 2) { // One option is the placeholder
                        deviceDropdown.children("option").eq(1).prop('selected', true);
                        deviceDropdown.change();
                    }
                }

                $("#campaign_device").change(function() {
                    var uuid = $("#campaign_device").val();
                    device_data = <?php echo json_encode($device_data, 15, 512) ?>;

                    if (uuid == -1) {
                        $('#template_select').empty().append(
                            '<option value="-1">-- Select Template --</option>');
                        return;
                    }
                    let templates = device_data[uuid]['templates'];
                    // console.log(templates);
                    $('#template_select').empty().append(
                        '<option value="-1">-- Select Template --</option>');

                    $.each(templates, function(index, value) {
                        if (value.status == "APPROVED") {
                            $('#template_select').append("<option value='" + value.name +
                                "'>" + value.name + " -- " + value.language +
                                "</option>");
                        }
                    });
                });

                // Call the function to auto-select if there's only one device
                selectSingleDevice();
            });
            $("#template_select").change(function() {
                $("#temp_head_image").hide();
                $("#msg_media").hide();
                $("#head_image").hide();
                var id = $("#template_select").val();
                device_data = <?php echo json_encode($device_data, 15, 512) ?>;
                var uuid = $("#campaign_device").val();
                let templates = device_data[uuid]['templates'];
                for (var i = 0; i < templates.length; i++) {
                    if (templates[i]['name'] == id) {
                        var head_type = templates[i]['components'][0]['format'];
                        var language = templates[i]['language'];

                        $("#language").val(language);

                        // Dynamically generate JavaScript code based on head_type
                        if (head_type === "IMAGE") {
                            var head_image =
                                'https://cdn4.iconfinder.com/data/icons/file-extensions-1/64/pngs-512.png';
                            if (templates[i]['components'][0]['example']) {
                                head_image = templates[i]['components'][0]['example'][
                                    'header_handle'
                                ][0];
                            }
                            var text = templates[i]['components'][1]['text'];
                            // Generate code for IMAGE
                            $("#temp_head_image").show();
                            $("#media_file").removeAttr("disabled");
                            $("#msg_media").show();
                            $("#head_text").hide();
                            $("#head_image").show();
                            $("#has_media").val("1");
                            $("#task_type").val("2");

                            var image = new Image();
                            image.crossOrigin = 'anonymous';
                            image.src = head_image;
                            image.onload = function() {
                                //print the image
                                $("#head_image").attr("src", this.src);
                                var c = document.createElement("canvas");
                                c.width = this.width;
                                c.height = this.height;
                                this.ctx = c.getContext("2d");
                                this.ctx.drawImage(this, 0, 0);
                                try {
                                    var imageDat = this.ctx.getImageData(0, 0, this.width,
                                        this.height);
                                } catch (e) {
                                    alert("Cross origin restricted access");
                                }
                            };
                        } else if (head_type === "DOCUMENT") {
                            // Generate code for DOCUMENT
                            var head_document =
                                'https://cdn4.iconfinder.com/data/icons/file-extensions-1/64/pdfs-512.png';
                            if (templates[i]['components'][0]['example']) {
                                head_document = templates[i]['components'][0]['example'][
                                    'header_handle'
                                ][0];
                            }
                            var text = templates[i]['components'][1]['text'];
                            $("#temp_head_video").show();
                            $("#media_file").removeAttr("disabled");
                            $("#msg_media").show();
                            $("#head_text").hide();
                            $("#head_image").show();
                            $("#has_media").val("1");
                            $("#task_type").val("3");
                            $("#head_video").attr("src", head_document);

                            var image = new Image();
                            image.crossOrigin = 'anonymous';
                            image.src =
                                'https://cdn4.iconfinder.com/data/icons/file-extensions-1/64/pdfs-512.png';
                            image.onload = function() {
                                //print the image
                                $("#head_image").attr("src", this.src);
                                var c = document.createElement("canvas");
                                c.width = this.width;
                                c.height = this.height;
                                this.ctx = c.getContext("2d");
                                this.ctx.drawImage(this, 0, 0);
                                try {
                                    var imageDat = this.ctx.getImageData(0, 0, 50, 50);
                                } catch (e) {
                                    alert("Cross origin restricted access");
                                }
                            };

                        } else if (head_type === "VIDEO") {
                            // Generate code for VIDEO
                            var head_video =
                                'https://cdn-icons-png.flaticon.com/512/9695/9695761.png';
                            if (templates[i]['components'][0]['example']) {
                                head_video = templates[i]['components'][0]['example'][
                                    'header_handle'
                                ][0];
                            }
                            var text = templates[i]['components'][1]['text'];
                            $("#temp_head_video").show();
                            $("#media_file").removeAttr("disabled");
                            $("#msg_media").show();
                            $("#head_text").hide();
                            $("#head_image").show();
                            $("#has_media").val("1");
                            $("#task_type").val("4");
                            $("#head_video").attr("src", head_video);
                            var image = new Image();
                            image.crossOrigin = 'anonymous';
                            image.src =
                                'https://cdn-icons-png.flaticon.com/512/9695/9695761.png';
                            image.onload = function() {
                                //print the image
                                $("#head_image").attr("src", this.src);
                                var c = document.createElement("canvas");
                                c.width = this.width;
                                c.height = this.height;
                                this.ctx = c.getContext("2d");
                                this.ctx.drawImage(this, 0, 0);
                                try {
                                    var imageDat = this.ctx.getImageData(0, 0, this.width,
                                        this.height);
                                } catch (e) {
                                    alert("Cross origin restricted access");
                                }
                            };
                        } else if (head_type === "TEXT") {
                            // Generate code for TEXT
                            var head_text = templates[i]['components'][0]['text'];
                            var text = templates[i]['components'][1]['text'];
                            $("#temp_head_text").show();
                            $("#has_media").val("0");
                            $("#task_type").val("1");
                            $("#head_text").text(head_text);
                            $("#head_text").show();
                            $("#head_image").hide();
                        } else {
                            // Handle other cases
                            $("#has_media").val("0");
                            $("#task_type").val("1");
                            var text = templates[i]['components'][0]['text'];
                        }
                        // Rest of your JavaScript code
                        $("#temp_body_text").show();
                        // $("#campaign_text").val(text);
                        var campaignText = document.querySelector("#campaign_text");
                        campaignText.innerHTML = formatWhatsAppTextToHTML(text);

                        templates[i]['components'].forEach(function(item, index) {

                            if (item['type'] == "BUTTONS") {

                                variable_text = '<div class="form-group form-float">';
                                item['buttons'].forEach(function(item, index) {
                                    $("#temp_buttons").show();
                                    //delte all child elements of temp_params
                                    $("#temp_buttons").empty();
                                    var button_text = item['text'];
                                    var button_url = item['url'];
                                    var button_phone_number = item[
                                        'phone_number'];
                                    var button_type = item['type'];
                                    if (button_type == 'URL') {
                                        variable_text += '<a href="' +
                                            button_url +
                                            '" class="btn btn-sm btn-neutral btn-block" target="_blank" margin-bottom:5px;" ><i class="fi fi-rs-arrow-up-right-from-square"></i>&nbsp;&nbsp;' +
                                            button_text + '</a>';
                                    } else if (button_type == 'QUICK_REPLY') {
                                        variable_text +=
                                            '<a href="#" class="btn btn-sm btn-neutral btn-block" target="_blank" margin-bottom:5px;" ><i class="fi fi-rs-reply-all"></i>&nbsp;&nbsp;' +
                                            button_text + '</a>';
                                    } else if (button_type == 'PHONE_NUMBER') {
                                        variable_text += '<a href="' +
                                            button_phone_number +
                                            '" class="btn btn-sm btn-neutral btn-block" target="_blank" margin-bottom:5px;" ><i class="fi fi-rs-circle-phone-flip"></i>&nbsp;&nbsp;' +
                                            button_text + '</a>';
                                    }
                                    $("#temp_buttons").append(variable_text);
                                });
                            } else {
                                $("#temp_buttons").hide();
                            }
                        });
                    }
                }

                let variableText = document.querySelector("#campaign_text").innerHTML;
                variableText = variableText.replace(/{/gi, "[");
                variableText = variableText.replace(/}/gi, "]");
                var variables = variableText.match(/\[\[[^\s\[\]]+\]\]/g);
                if (variables != null) {
                    $("#temp_params").show();
                    $("#temp_params").empty(); // Clear existing content

                    // Generate input fields for variables
                    var variable_text = '<div class="form-group row">';
                    for (var i = 0; i < variables.length; i++) {
                        var variable = variables[i].replace("[[", "").replace("]]", "");
                        variable_text +=
                            '<div class="col-sm-12"><input type="text" class="form-control" id="variable_' +
                            variable + '" name="variable[]" placeholder="Enter Value : ' +
                            variable +
                            '" required></div>';
                    }
                    variable_text += '</div>';

                    $("#temp_params").append(variable_text); // Add input fields
                } else {
                    $("#temp_params").hide();
                }

            });
        });
    </script>

    <script>
        const tutorial = <?php echo json_encode($videoTutorial, 15, 512) ?>;

        // Function to extract video ID from YouTube URL
        function getVideoId(url) {
            const regex =
                /(?:https?:\/\/)?(?:www\.)?(?:youtu\.be\/|(?:www\.)?youtube\.com\/(?:watch\?v=|embed\/|v\/|.+\?v=))([^&]+)/;
            const matches = url.match(regex);
            return matches ? matches[1] : null;
        }

        // Event listener for the modal opening
        $('#videoModal').on('show.bs.modal', function() {
            const videoId = getVideoId(tutorial.value); // Extract video ID from the tutorial URL
            if (videoId) {
                const videoUrl = `https://www.youtube.com/embed/${videoId}`;

                // Set the video URL to the iframe
                document.getElementById('youtube-video').src = videoUrl;
            } else {
                console.error('Invalid YouTube URL');
            }
        });

        // Clear the video source when the modal is hidden
        $('#videoModal').on('hidden.bs.modal', function() {
            document.getElementById('youtube-video').src = '';
        });
    </script>
<?php $__env->stopPush(); ?>

<?php echo $__env->make('layouts.main.app', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\Users\<USER>\OneDrive\Vishal\xampp\htdocs\waba_panelv2\resources\views/user/dynamicsend/create.blade.php ENDPATH**/ ?>