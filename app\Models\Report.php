<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class Report extends Model
{
    use HasFactory;

    public function get_data($start_date, $end_date, $draw, $start, $length, $search_value, $columnname, $columnsortorder, $device_id, $campaign_id)
    {
        $totalRecords = Task::where('created_by', Auth::id())
            ->whereDate('scheduled_on', '>=', $start_date)
            ->whereDate('scheduled_on', '<=', $end_date)
            ->when($device_id, function ($query) use ($device_id) {
                return $query->where('task.device_id', $device_id);
            })
            ->when($campaign_id, function ($query) use ($campaign_id) {
                return $query->where('task.campaign_name', $campaign_id);
            })
            ->when($search_value, function ($query) use ($search_value) {
                return $query->where(function ($subquery) use ($search_value) {
                    return  $subquery->where('task.send_to_number', 'LIKE', "%$search_value%")
                        ->orWhere('task.campaign_name', 'LIKE', "%$search_value%");
                });
            })
            ->count();

        $timestampColumns = ['launched_on', 'whatsapp_sent_time', 'whatsapp_received_time', 'whatsapp_read_time'];

        $results = DB::table('task')
            ->join('devices', 'task.device_id', '=', 'devices.id')
            ->leftJoin('templates', function ($join) {
                $join->on('task.templateId', '=', 'templates.id');
            })
            ->select(
                'task.task_id',
                'task.campaign_name',
                'devices.name as name',
                'devices.phone as phone',
                'task.launched_on',
                'task.scheduled_on',
                'task.whatsapp_sent_time',
                'task.whatsapp_received_time',
                'task.whatsapp_read_time',
                'task.send_to_number',
                'task.templateId',
                'task.task_description',
                DB::raw($this->getCoalesceExpression()),
                DB::raw("CASE
            WHEN task.task_type = 1 THEN 'TEXT'
            WHEN task.task_type = 2 THEN 'IMAGE'
            WHEN task.task_type = 3 THEN 'PDF'
            WHEN task.task_type = 4 THEN 'VIDEO'
            ELSE 'UNKNOWN'
            END as task_type"),
                'task.text',
                'task.task_url',
                DB::raw("CASE
            WHEN task.task_status = 0 THEN 'Pending'
            WHEN task.task_status = 1 THEN 'sent'
            WHEN task.task_status = 3 THEN 'Read'
            WHEN task.task_status = 2 THEN 'delivered'
            WHEN task.task_status = 4 THEN 'failed'
            ELSE 'UNKNOWN'
            END as status_text")
            )

            ->where('task.created_by', Auth::id())
            ->whereDate('scheduled_on', '>=', $start_date)
            ->whereDate('scheduled_on', '<=', $end_date)
            ->when($device_id, function ($query) use ($device_id) {
                return $query->where('task.device_id', $device_id);
            })
            ->when($campaign_id, function ($query) use ($campaign_id) {
                return $query->where('task.campaign_name', $campaign_id);
            })
            ->when($search_value, function ($query) use ($search_value) {
                return $query->where(function ($subquery) use ($search_value) {
                    return  $subquery->where('task.send_to_number', 'LIKE', "%$search_value%")
                        ->orWhere('task.campaign_name', 'LIKE', "%$search_value%")
                        ->orWhere('task.templateId', 'LIKE', "%$search_value%");
                });
            })
            ->orderBy('task.task_id', 'DESC')
            ->skip($start)
            ->take($length)
            ->get()->toArray();

        $response = [
            "draw" => intval($draw),
            "iTotalRecords" => $totalRecords,
            "iTotalDisplayRecords" => $totalRecords,
            "aaData" => $results,
        ];

        return response()->json($response);
    }

    // public function export_data($start_date, $end_date, $device_id, $campaign_id)
    // {
    //     $results = DB::table('task')
    //         ->join('devices', 'task.device_id', '=', 'devices.id')
    //         ->select(
    //             // 'task.task_id',
    //             'devices.phone as phone',
    //             'task.whatsapp_sent_time',
    //             'task.whatsapp_received_time',
    //             'task.whatsapp_read_time',
    //             'task.campaign_name',
    //             'task.send_to_number',
    //             'task.task_url',
    //             'task.templateId',
    //             DB::raw("CASE
    //             WHEN task.task_type = 1 THEN 'TEXT'
    //             WHEN task.task_type = 2 THEN 'IMAGE'
    //             WHEN task.task_type = 3 THEN 'PDF'
    //             WHEN task.task_type = 4 THEN 'VIDEO'
    //             ELSE 'UNKNOWN' END as task_type"),
    //             DB::raw("CASE
    //             WHEN task.task_status = 0 THEN 'Pending'
    //             WHEN task.task_status = 1 THEN 'Sent'
    //             WHEN task.task_status = 3 THEN 'Read'
    //             WHEN task.task_status = 2 THEN 'Delivered'
    //             WHEN task.task_status = 4 THEN 'Failed'
    //             ELSE 'UNKNOWN' END as status_text"),
    //             'task.parameters'
    //         )
    //         ->where('task.created_by', Auth::id())
    //         ->whereDate('scheduled_on', '>=', $start_date)
    //         ->whereDate('scheduled_on', '<=', $end_date)
    //         ->when($device_id, function ($query) use ($device_id) {
    //             return $query->where('task.device_id', $device_id);
    //         })
    //         ->when($campaign_id, function ($query) use ($campaign_id) {
    //             return $query->where('task.campaign_name', $campaign_id);
    //         })

    //         // ->orderBy('task.task_id', 'ASC')
    //         ->get();

    //     return $results->toArray();
    // }

    /**
     * Get database-agnostic COALESCE expression for template name
     * Works with both MySQL/SQL Server and PostgreSQL
     */
    private function getCoalesceExpression()
    {
        $driver = DB::connection()->getDriverName();

        switch ($driver) {
            case 'pgsql':
                // PostgreSQL - use double quotes for case-sensitive column names
                return 'COALESCE(templates.title, task."templateId") as template_name';

            case 'mysql':
            case 'sqlsrv':
            case 'sqlite':
            default:
                // MySQL, SQL Server, SQLite - use backticks or no quotes
                return 'COALESCE(templates.title, task.templateId) as template_name';
        }
    }
}
