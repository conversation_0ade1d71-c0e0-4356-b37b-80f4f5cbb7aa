<?php

use Illuminate\Support\Facades\Route;
use App\Http\Controllers\User as USER;

Route::group(['prefix' => 'user', 'as' => 'user.', 'middleware' => ['auth', 'user', 'saas', 'user.checkpermission']], function () {

    //all dashboard routes
    Route::get('dashboard', [USER\DashboardController::class, 'index'])->name('dashboard.index');
    Route::get('dashboard-static-data', [USER\DashboardController::class, 'dashboardData'])->name('dashboard.static');
    Route::get('messages-transaction/{days}', [USER\DashboardController::class, 'getMessagesTransaction'])->name('messages.static');
    Route::get('chatbot-transaction/{days}', [USER\DashboardController::class, 'getChatbotTransaction'])->name('chatbot.static');
    Route::get('messages-types-transaction/{days}', [USER\DashboardController::class, 'messagesStatics'])->name('types.static');

    //device routes
    // Route::resource('device', USER\DeviceController::class);

    Route::get('account/indiamart_integration/{uuid}', [USER\DeviceController::class, 'indiamartIntegration'])->name('account.indiamartIntegration');
    Route::post('account/indMartIntStore', [USER\DeviceController::class, 'indMartIntStore'])->name('account.indMartIntStore');
    Route::get('/delete-im-integration/{uuid}', [USER\DeviceController::class, 'deleteImIntegration'])->name('deleteImIntegration');

    Route::post('embeded-signup', [USER\DeviceController::class, 'embededSignup'])->name('device.embeded-signup');
    // Route::get('device/{id}/qr', [USER\DeviceController::class, 'scanQr'])->name('device.scan');
    Route::resource('account', USER\DeviceController::class);
    Route::get('account/{id}/qr', [USER\DeviceController::class, 'scanQr'])->name('account.scan');

    Route::post('create-session/{id}', [USER\DeviceController::class, 'getQr']);
    Route::post('check-session/{id}', [USER\DeviceController::class, 'checkSession']);
    Route::post('/logout-session/{id}', [USER\DeviceController::class, 'logoutSession']);
    Route::post('/device-statics', [USER\DeviceController::class, 'deviceStatics']);
    Route::get('/status-update/{uuid}', [USER\DeviceController::class, 'statusUpdate'])->name('statusUpdate');
    Route::post('/autosend-mkt-fail-msg/{uuid}', [USER\DeviceController::class, 'autoSendMktFailMsg'])->name('autoSendMktFailMsg');
    Route::post('/block-unblock-user', [USER\DeviceController::class, 'blockUnblockUser'])->name('blockUnblockUser');

    Route::get('/account/chats/{uuid}', [USER\ChatController::class, 'chats'])->name('device.chats');
    Route::get('/markread/{uuid}/{number}', [USER\ChatController::class, 'markread_chats']);
    Route::get('/chats-history/{uuid}/{number}', [USER\ChatController::class, 'chats_history']); //get perticular chat history
    Route::post('/get-chats/{uuid}', [USER\ChatController::class, 'chatHistory']); //get chat list numbers
    Route::post('/send-message/{uuid}', [USER\ChatController::class, 'sendMessage'])->name('chat.send-message');

    Route::post('/chat/delete/{device_uuid}/{number}', [USER\ChatController::class, 'deleteChat'])->name('chat.delete');

    // Route::get('/device/groups/{uuid}', [USER\ChatController::class, 'groups']);
    Route::get('/account/chats/{uuid}', [USER\ChatController::class, 'chats']);
    Route::post('/get-groups/{uuid}', [USER\ChatController::class, 'groupHistory']);
    Route::post('/send-group-message/{uuid}', [USER\ChatController::class, 'sendGroupMessage'])->name('group.send-message');
    Route::post('/send-group-bulk-message/{uuid}', [USER\ChatController::class, 'sendGroupBulkMessage'])->name('group.bulk.send-message');

    Route::post('/get-group-metadata', [USER\ChatController::class, 'getGroupMetaData'])->name('group.matadata');

    //app routes
    Route::resource('apps', USER\AppsController::class);
    Route::get('/app/integration/{uuid}', [USER\AppsController::class, 'integration'])->name('app.integration');
    Route::get('/app/messages-logs/{uuid}', [USER\AppsController::class, 'logs'])->name('app.logs');

    //template routes
    Route::resource('template', USER\TemplateController::class);
    Route::get('viewtemplate/{uuid}', [USER\TemplateController::class, 'viewtemplate'])->name('template.viewtemplate');
    Route::get('/template/create/{device_id}', [USER\TemplateController::class, 'create'])->name('template.create-now');

    //single send or custom text routes
    Route::get('/sent-text-message', [USER\CustomTextController::class, 'index']);
    Route::post('/sent-whatsapp-custom-text', [USER\CustomTextController::class, 'sentCustomText'])->name('sent.customtext');
    Route::get('/dynamic-sent-text-message', [USER\CustomDynamicTextController::class, 'index']);
    Route::post('/dynamic-whatsapp-custom-text', [USER\CustomDynamicTextController::class, 'DynamicCustomText'])->name('sent.DynamicCustomText');

    //bulk sender routes
    // Route::post('/bulk-messages',                          [USER\BulkController::class, 'store'])->name('bulk-message.store');
    Route::resource('/bulk-message', USER\BulkController::class);
    Route::get('bulk-message/template-with-message/create', [USER\BulkController::class, 'templateWithMessage']);
    Route::get('/sent-bulk-with-template/{id}/{groupid}/{deviceid}', [USER\BulkController::class, 'sendBulkToContacts']);
    Route::post('/sent-message-with-template', [USER\BulkController::class, 'sendMessageToContact']);
    //schedule message routes
    Route::resource('schedule-message', USER\ScheduleController::class);
    //schedule message routes
    Route::resource('contact', USER\ContactController::class);
    Route::get('export-contacts', [USER\ContactController::class, 'exportContacts'])->name('export.contacts');
    Route::post('contact', [USER\ContactController::class, 'sendtemplateBulk'])->name('contact.send-template-bulk');
    Route::post('contact/store', [USER\ContactController::class, 'store'])->name('contact.store');
    Route::post('contact-import', [USER\ContactController::class, 'import'])->name('contact.import');
    Route::get('blacklist', [USER\ContactController::class, 'blacklistContacts'])->name('blacklistContacts');

    Route::resource('response', USER\ResponseController::class);
    Route::post('get_response_data_filter', [USER\ResponseController::class, 'get_response_data_filter'])->name('get_response_data_filter');
    Route::get('export_response_data', [USER\ResponseController::class, 'export_response_data'])->name('export_response_data');

    Route::resource('flow_report', USER\FlowReportController::class);
    Route::post('getFlowReportDataFilter', [USER\FlowReportController::class, 'getFlowReportDataFilter'])->name('getFlowReportDataFilter');
    Route::get('exportFlowData', [USER\FlowReportController::class, 'exportFlowData'])->name('exportFlowData');

    Route::resource('report', USER\ReportController::class);
    Route::post('get_data_filter', [USER\ReportController::class, 'get_data_filter'])->name('get_data_filter');
    Route::get('export_data', [USER\ReportController::class, 'export_data'])->name('export_data');

    //greetings rotutes
    Route::get('greetings', [USER\GreetingsController::class, 'index']);
    Route::post('greetings/store', [USER\GreetingsController::class, 'store'])->name('greetings.store');

    //chatbot route
    Route::resource('chatbot', USER\ChatbotController::class);
    Route::get('get_templates/{device_id}', [USER\ChatbotController::class, 'getTemplates'])->name('get_templates');

    Route::resource('tag', USER\TagController::class);
    Route::post('create_tag', [USER\TagController::class, 'create_tag'])->name('create_tag');
    Route::get('get_tag_option_data', [USER\TagController::class, 'tag_option_data_ajax'])->name('tag_option_data');
    Route::post('store-contact-tag', [USER\TagController::class, 'store_contact_tag'])->name('store_contact_tag');
    Route::post('update_assign_chat', [USER\ChatController::class, 'update_assign_chat'])->name('update_assign_chat');
    Route::post('send_note_msg', [USER\ChatController::class, 'send_note_msg'])->name('send_note_msg');
    Route::post('delete_chat_message', [USER\ChatController::class, 'delete_chat_message'])->name('delete_chat_message');

    Route::resource('facebook_leads', USER\FbLeadsAdsController::class);
    Route::get('facebook_leads/list', [USER\FbLeadsAdsController::class, 'fbLeadIntList'])->name('fbLeadIntList');
    // Route::get('facebook_leads', [USER\FbLeadsAdsController::class, 'facebookLeadsAds'])->name('facebookLeadsAds');
    Route::post('fb_lead_ads_signup', [USER\FbLeadsAdsController::class, 'fbLeadAdSignup'])->name('fbLeadAdSignup');
    Route::get('leadAccessToken/{accessToken}', [USER\FbLeadsAdsController::class, 'leadAccessToken'])->name('leadAccessToken');
    Route::get('/delete-fbToken', [USER\FbLeadsAdsController::class, 'deleteFbToken'])->name('deleteFbToken');

    Route::resource('indiamart_leads', USER\ImLeadsController::class);

    Route::resource('deletechat', USER\DeleteChatController::class);
    Route::post('deleteChatHistory', [USER\DeleteChatController::class, 'deleteChatHistory'])->name('deleteChatHistory');
    Route::post('deletePendingMsg', [USER\DeleteChatController::class, 'deletePendingMsg'])->name('deletePendingMsg');

    Route::resource('drip_campaign', USER\DripCampaignController::class);
    Route::post('get_drip_name', [USER\DripCampaignController::class, 'getDripName'])->name('drip_campaign.get_drip_name');
    Route::post('send_drip', [USER\DripCampaignController::class, 'sendDrip'])->name('drip_campaign.send_drip');

    Route::resource('bot_builder', USER\BotBuilderController::class);
    Route::post('bot_builder_device', [USER\BotBuilderController::class, 'botBuilderDevice'])->name('bot_builder_device');

    //log report route
    // Route::resource('logs',                                USER\LogController::class);
    //profile settings
    Route::get('profile', [USER\ProfileController::class, 'settings']);
    Route::put('profile/update/{type}', [USER\ProfileController::class, 'update'])->name('profile.update');
    Route::get('auth-key', [USER\ProfileController::class, 'authKey']);
    //help and support routes
    Route::resource('support', USER\SupportController::class);
    //subscription / plan route
    //fcm token store
    Route::post('fcm-token', [USER\FcmController::class, 'savetoken'])->name('fcm-token');

    if (request()->getHttpHost() == 'dmsv2.nxccontrols.in') {
        Route::resource('subscription', USER\SubscriptionController::class);
        Route::post('make-subscribe/{gateway_id}/{plan_id}', [USER\SubscriptionController::class, 'subscribe'])->name('make-payment');
        Route::get('/subscription/plan/{status}', [USER\SubscriptionController::class, 'status']);
        Route::get('/subscriptions/log', [USER\SubscriptionController::class, 'log']);
        Route::get('/subscription-history', [USER\SubscriptionController::class, 'log']);
    } else {
        Route::get('/subscriptions/log', [USER\SubscriptionController::class, 'log']);
        Route::get('/subscription-history', [USER\SubscriptionController::class, 'log']);
    }

    Route::resource('notifications', USER\NotificationController::class);
    Route::resource('group', USER\GroupController::class);

    //subuser
    Route::resource('agent', USER\AgentController::class);
    // Route::resource('role', USER\AgentRoleController::class);
});
//list all role names based on above routes
