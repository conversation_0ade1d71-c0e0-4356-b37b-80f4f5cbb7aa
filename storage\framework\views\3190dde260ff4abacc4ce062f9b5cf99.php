
<?php $__env->startSection('head'); ?>
    <?php echo $__env->make('layouts.main.headersection', [
        'title' => __('Create User'),
        'buttons' => [
            [
                'name' => __('Back'),
                'url' => route('admin.customer.index'),
            ],
        ],
    ], \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
<?php $__env->stopSection(); ?>
<?php $__env->startSection('content'); ?>
    <div class="row ">
        <div class="col-lg-5 mt-5">
            <strong><?php echo e(__('Create User')); ?></strong>
            <p><?php echo e(__('Creat user profile information')); ?></p>
        </div>
        <div class="col-lg-7 mt-5">
            <form class="ajaxform_instant_reload" action="<?php echo e(route('admin.customer.store')); ?>">
                <?php echo csrf_field(); ?>
                <div class="card">
                    <div class="card-body">
                        <div class="from-group row mt-2">
                            <label class="col-lg-12"><?php echo e(__('Name')); ?></label>
                            <div class="col-lg-12">
                                <input type="text" name="name" required="" class="form-control">
                            </div>
                        </div>
                        <div class="from-group row mt-2">
                            <label class="col-lg-12"><?php echo e(__('Phone (optional)')); ?></label>
                            <div class="col-lg-12">
                                <input type="text" name="phone" class="form-control">
                            </div>
                        </div>
                        <div class="from-group row mt-2">
                            <label class="col-lg-12"><?php echo e(__('Email')); ?></label>
                            <div class="col-lg-12">
                                <input type="email" name="email" required="" class="form-control">
                            </div>
                        </div>
                        <div class="from-group row mt-2">
                            <label class="col-lg-12"><?php echo e(__('Password')); ?></label>
                            <div class="col-lg-12">
                                <input type="text" name="password" required="" class="form-control" value="">
                            </div>
                        </div>
                        <div class="from-group row mt-3">
                            <div class="col-lg-12">
                                <button class="btn btn-neutral submit-button btn-sm float-left">
                                    <?php echo e(__('Create')); ?></button>
                            </div>
                        </div>
                    </div>
                </div>
            </form>
        </div>
    </div>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.main.app', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\Users\<USER>\OneDrive\Vishal\xampp\htdocs\waba_panelv2\resources\views/admin/customers/create.blade.php ENDPATH**/ ?>