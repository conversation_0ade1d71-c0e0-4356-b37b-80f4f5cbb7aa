
<?php $__env->startSection('head'); ?>
    <?php echo $__env->make('layouts.main.headersection', [
        'title' => __('Drip Campaign'),
        'buttons' => array_filter([
            isset($videoTutorial->value)
                ? [
                    'name' => '<i class="fi-rs-photo-video"></i>&nbsp&nbsp' . __('Video Tutorial'),
                    'url' => '#',
                    'components' => 'data-toggle="modal" data-target="#videoModal"',
                    'is_button' => true,
                ]
                : null,
        ]),
    ], \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
<?php $__env->stopSection(); ?>
<?php $__env->startPush('topcss'); ?>
    
    <link rel="stylesheet" type="text/css" href="<?php echo e(asset('assets/vendor/select2/dist/css/select2.min.css')); ?>">
<?php $__env->stopPush(); ?>
<?php $__env->startSection('content'); ?>
    <div class="row justify-content-center">
        <div class="col-12">


            <!-- Modal for YouTube video -->
            <div class="modal fade" id="videoModal" tabindex="-1" role="dialog" aria-labelledby="videoModalLabel"
                aria-hidden="true">
                <div class="modal-dialog modal-dialog-centered" role="document">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title" id="videoModalLabel"><?php echo e($videoTutorial->title ?? ''); ?></h5>
                            <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                                <span aria-hidden="true">&times;</span>
                            </button>
                        </div>
                        <div class="modal-body">
                            <iframe id="youtube-video" width="100%" height="315" src="" frameborder="0"
                                allowfullscreen></iframe>
                        </div>
                    </div>
                </div>
            </div>

            <?php if(getUserPlanData('access_bot_builder') == true): ?>
                <?php if(count($devices ?? []) > 0): ?>
                    <div class="row">
                        <?php $__currentLoopData = $devices ?? []; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $device): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <div class="col-xl-4 col-md-6">
                                <div class="card  border-0">
                                    <!-- Card body -->
                                    <div class="card-body">
                                        <div class="row">
                                            <div class="col">
                                                <h5 class="card-title text-uppercase text-muted mb-0 text-dark">
                                                    <?php echo e($device->name); ?>

                                                </h5>
                                                <div class="mt-3 mb-0">
                                                    <span class="pt-2 text-dark"><?php echo e(__('Phone :')); ?>

                                                        <span
                                                            style="color: #657bf8; font-weight: bold;"><?php echo e($device->phone); ?></span>
                                                    </span>
                                                    <br>
                                                    <span class="pt-2 text-dark"><?php echo e(__('Email :')); ?>

                                                        <span
                                                            style="color: #657bf8; font-weight: bold;"><?php echo e($device->user->email); ?></span>
                                                    </span>
                                                    <br>
                                                </div>
                                            </div>

                                        </div>
                                        <p class="mt-3 mb-0 text-sm">
                                            
                                            <span class="text-dark"><?php echo e(__('Status :')); ?></span>
                                            <span class="badge badge-sm <?php echo e(badge($device->status)['class']); ?>">
                                                <?php echo e($device->status == 1 ? __('Active') : __('Inactive')); ?>

                                            </span>
                                            

                                            <button class="btn btn-outline-primary submit-button btn-sm float-right"
                                                onclick="getDeviceDetail(this)" data-name="<?php echo e($device->name); ?>"
                                                data-phone="<?php echo e($device->phone); ?>">
                                                <?php echo e(__('Go')); ?>

                                            </button>

                                        </p>
                                    </div>
                                </div>
                            </div>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    </div>
                <?php else: ?>
                    <div class="alert  bg-gradient-primary text-white"><span
                            class="text-left"><?php echo e(__('Opps There Is No Device Found....')); ?></span></div>
                <?php endif; ?>
            <?php else: ?>
                <div class="col-sm-12">
                    <div class="alert bg-gradient-primary text-white alert-dismissible fade show" role="alert">
                        <span class="alert-icon"><i class="fi  fi-rs-info text-white"></i></span>
                        <span class="alert-text">
                            <strong><?php echo e(__('!Opps ')); ?></strong>

                            <?php echo e(__('Bot Builder access features is not available in your subscription plan')); ?>


                        </span>
                    </div>
                </div>
            <?php endif; ?>
        </div>
    </div>

<?php $__env->stopSection(); ?>
<?php $__env->startPush('js'); ?>
    <script src="<?php echo e(asset('assets/vendor/select2/dist/js/select2.min.js')); ?>"></script>
<?php $__env->stopPush(); ?>
<?php $__env->startPush('js'); ?>
    <script>
        const tutorial = <?php echo json_encode($videoTutorial, 15, 512) ?>;

        // Function to extract video ID from YouTube URL
        function getVideoId(url) {
            const regex =
                /(?:https?:\/\/)?(?:www\.)?(?:youtu\.be\/|(?:www\.)?youtube\.com\/(?:watch\?v=|embed\/|v\/|.+\?v=))([^&]+)/;
            const matches = url.match(regex);
            return matches ? matches[1] : null;
        }

        // Event listener for the modal opening
        $('#videoModal').on('show.bs.modal', function() {
            const videoId = getVideoId(tutorial.value); // Extract video ID from the tutorial URL
            if (videoId) {
                const videoUrl = `https://www.youtube.com/embed/${videoId}`;

                // Set the video URL to the iframe
                document.getElementById('youtube-video').src = videoUrl;
            } else {
                console.error('Invalid YouTube URL');
            }
        });

        // Clear the video source when the modal is hidden
        $('#videoModal').on('hidden.bs.modal', function() {
            document.getElementById('youtube-video').src = '';
        });
    </script>

    <script>
        function getDeviceDetail(button) {
            // Get the value of the hidden input
            var name = button.getAttribute('data-name');
            var number = button.getAttribute('data-phone');

            // console.log('name', name);
            // console.log('number', number);

            // Check if the number exists
            if (name && number) {

                var data = {
                    name: name,
                    number: number,
                };

                $.ajax({
                    url: "<?php echo e(route('user.bot_builder_device')); ?>",
                    type: "POST",
                    data: data,
                    dataType: "JSON",
                    headers: {
                        'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
                    },
                    success: function(data) {
                        console.log('bot_builder_device data', data);
                        if (data.success) {
                            console.log("Success: " + data.message);

                            if (data.email) {
                                console.log('send invitation on email');
                                var redirectUrl = 'https://tb.nxc.co.in/signin';

                                window.open(redirectUrl, '_blank');
                            }
                        } else {
                            console.log("Error: " + data.message);
                        }
                    },
                    error: function(jqXHR, textStatus, errorThrown) {
                        if (jqXHR.responseJSON && jqXHR.responseJSON.message) {
                            console.error("Error: " + jqXHR.responseJSON
                                .message); // Correct way to access the error message
                        } else {
                            console.error("Unknown error: " +
                                errorThrown); // In case the message isn't available
                        }
                    }
                });

            } else {
                console.log('Please provide both name and number');
            }
        }
    </script>
<?php $__env->stopPush(); ?>

<?php echo $__env->make('layouts.main.app', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\Users\<USER>\OneDrive\Vishal\xampp\htdocs\waba_panelv2\resources\views/user/bot_builder/index.blade.php ENDPATH**/ ?>