
<?php $__env->startSection('head'); ?>
    <?php echo $__env->make('layouts.main.headersection', [
        'title' => 'Automatic Replies',
        'buttons' => array_filter([
            isset($videoTutorial->value)
                ? [
                    'name' => '<i class="fi-rs-photo-video"></i>&nbsp&nbsp' . __('Video Tutorial'),
                    'url' => '#',
                    'components' => 'data-toggle="modal" data-target="#videoModal"',
                    'is_button' => true,
                ]
                : null,
            [
                'name' => '<i class="fas fa-plus"></i> &nbspCreate Reply',
                'url' => '#',
                'components' => 'data-toggle="modal" data-target="#send-template-bulk" id="send-template-bulks"',
                'is_button' => true,
            ],
        ]),
    ], \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
<?php $__env->stopSection(); ?>
<?php $__env->startPush('topcss'); ?>
    <link rel="stylesheet" type="text/css" href="<?php echo e(asset('assets/vendor/select2/dist/css/select2.min.css')); ?>">
<?php $__env->stopPush(); ?>
<?php $__env->startSection('content'); ?>
    <div class="row justify-content-center">
        <div class="col-12">
            <div class="row d-flex justify-content-between flex-wrap">
                <div class="col">
                    <div class="card card-stats">
                        <div class="card-body">
                            <div class="row">
                                <div class="col">
                                    <span class="h2 font-weight-bold mb-0 total-transfers">
                                        <?php echo e(number_format($total_replies)); ?>

                                    </span>
                                </div>
                                <div class="col-auto">
                                    <div class="icon icon-shape bg-gradient-primary text-white rounded-circle shadow">
                                        <i class="fas  fa-robot"></i>
                                    </div>
                                </div>
                            </div>
                            <p class="mt-3 mb-0 text-sm">
                            </p>
                            <h5 class="card-title  text-muted mb-0"><?php echo e(__('Total Replies')); ?></h5>
                            <p></p>
                        </div>
                    </div>
                </div>
                <div class="col">
                    <div class="card card-stats">
                        <div class="card-body">
                            <div class="row">
                                <div class="col">
                                    <span class="h2 font-weight-bold mb-0 total-transfers">
                                        <?php echo e(number_format($template_replies)); ?>

                                    </span>
                                </div>
                                <div class="col-auto">
                                    <div class="icon icon-shape bg-gradient-primary text-white rounded-circle shadow">
                                        <i class="fi fi-rs-test mt-2"></i>
                                    </div>
                                </div>
                            </div>
                            <p class="mt-3 mb-0 text-sm">
                            </p>
                            <h5 class="card-title  text-muted mb-0"><?php echo e(__('Template Replies')); ?></h5>
                            <p></p>
                        </div>
                    </div>
                </div>
                <div class="col">
                    <div class="card card-stats">
                        <div class="card-body">
                            <div class="row">
                                <div class="col">
                                    <span class="h2 font-weight-bold mb-0 completed-transfers">
                                        <?php echo e(number_format($text_replies)); ?>

                                    </span>
                                </div>
                                <div class="col-auto">
                                    <div class="icon icon-shape bg-gradient-primary text-white rounded-circle shadow">
                                        <i class=" fi-rs-text-check mt-2"></i>
                                    </div>
                                </div>
                            </div>
                            <p class="mt-3 mb-0 text-sm">
                            </p>
                            <h5 class="card-title  text-muted mb-0"><?php echo e(__('Text Replies')); ?></h5>
                            <p></p>
                        </div>
                    </div>
                </div>
            </div>
            <?php if(getUserPlanData('chatbot') == false): ?>
                <div class="row">
                    <div class="col-sm-12">
                        <div class="alert bg-gradient-primary text-white alert-dismissible fade show" role="alert">
                            <span class="alert-icon"><i class="fi  fi-rs-info text-white"></i></span>
                            <span class="alert-text">
                                <strong><?php echo e(__('!Opps ')); ?></strong>

                                <?php echo e(__('Chatbot features is not available in your subscription plan')); ?>


                            </span>
                        </div>
                    </div>
                </div>
            <?php endif; ?>
            <?php if(count($replies ?? []) == 0): ?>
                <div class="row">
                    <div class="col-12">
                        <div class="card">
                            <div class="card-body">
                                <center>
                                    <img src="<?php echo e(asset('assets/img/404.jpg')); ?>" height="500">
                                    <h3 class="text-center"><?php echo e(__('!Opps You Have Not Created Automatic Reply')); ?></h3>
                                    <a href="#" data-toggle="modal" data-target="#send-template-bulk"
                                        id="send-template-bulks" class="btn btn-neutral"><i class="fas fa-plus"></i>
                                        <?php echo e(__('Create a reply')); ?></a>
                                </center>
                            </div>
                        </div>
                    </div>
                </div>
            <?php else: ?>
                <div class="card">
                    <div class="card-header border-0">
                        <h3 class="mb-0"><?php echo e(__('Replies')); ?></h3>
                        <form action="" class="card-header-form">
                            <div class="input-group">
                                <input type="text" name="search" value="<?php echo e($request->search ?? ''); ?>"
                                    class="form-control" placeholder="Search......">
                                <select class="form-control" name="type">
                                    <option value="keyword" <?php if($type == 'keyword'): ?> selected="" <?php endif; ?>>
                                        <?php echo e(__('Keyword')); ?>

                                    </option>
                                </select>
                                <div class="input-group-btn">
                                    <button class="btn btn-neutral btn-icon"><i class="fas fa-search"></i></button>
                                </div>
                            </div>
                        </form>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-sm-12 table-responsive">
                                <table class="table col-12">
                                    <thead>
                                        <tr>
                                            <th><?php echo e(__('Keyword')); ?></th>
                                            <th><?php echo e(__('Device')); ?></th>
                                            
                                            <th><?php echo e(__('Keyword Match Type')); ?></th>
                                            
                                            <th><?php echo e(__('Template')); ?></th>
                                            <th><?php echo e(__('Status')); ?></th>
                                            <th class="text-right"><?php echo e(__('Action')); ?></th>
                                        </tr>
                                    </thead>
                                    <tbody class="tbody">
                                        <?php $__currentLoopData = $replies ?? []; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $reply): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                            
                                            <tr>
                                                <td><?php echo e($reply->keyword); ?></td>
                                                <td><?php echo e($reply->device->name ?? ''); ?> <br>
                                                    <small>(<?php echo e($reply->device->phone ?? ''); ?>)</small>
                                                    
                                                </td>
                                                
                                                <td>
                                                    <?php echo e($reply->match_type == 'equal' ? 'Whole Word' : 'Similar Word'); ?>

                                                </td>
                                                <td>
                                                    <?php echo e($reply->template->title ?? ''); ?> (<?php echo e($reply->template->type ?? ''); ?>)
                                                </td>
                                                <td>
                                                    <span
                                                        class="badge badge-<?php echo e($reply->status == 1 ? 'success' : 'danger'); ?>">
                                                        <?php echo e($reply->status == 1 ? 'Enable' : 'Disable'); ?>

                                                    </span>
                                                </td>
                                                <td>
                                                    <div class="btn-group mb-2 float-right">
                                                        <button class="btn btn-neutral btn-sm dropdown-toggle"
                                                            type="button" data-toggle="dropdown" aria-haspopup="true"
                                                            aria-expanded="false">
                                                            <?php echo e(__('Action')); ?>

                                                        </button>
                                                        <div class="dropdown-menu">
                                                            <a class="dropdown-item has-icon edit-reply" href="#"
                                                                data-action="<?php echo e(route('user.chatbot.update', $reply->id)); ?>"
                                                                data-templateid="<?php echo e($reply->template_id); ?>"
                                                                data-keyword="<?php echo e($reply->keyword); ?>"
                                                                data-reply="<?php echo e($reply->reply); ?>"
                                                                data-agentjson="<?php echo e($reply->agent_json); ?>"
                                                                data-device="<?php echo e($reply->device_id); ?>"
                                                                data-status="<?php echo e($reply->status); ?>" data-toggle="modal"
                                                                data-target="#editModal">
                                                                <i class="fi fi-rs-edit"></i><?php echo e(__('Edit')); ?></a>
                                                            <a class="dropdown-item has-icon delete-confirm"
                                                                href="javascript:void(0)"
                                                                data-action="<?php echo e(route('user.chatbot.destroy', $reply->id)); ?>"><i
                                                                    class="fas fa-trash"></i><?php echo e(__('Remove Reply')); ?></a>
                                                        </div>
                                                    </div>
                                                </td>
                                            </tr>
                                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                    </tbody>
                                </table>
                                <div class="d-flex justify-content-center">
                                    <?php echo e($replies->links('vendor.pagination.bootstrap-4')); ?>

                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            <?php endif; ?>
        </div>
    </div>

    <!-- Modal for YouTube video -->
    <div class="modal fade" id="videoModal" tabindex="-1" role="dialog" aria-labelledby="videoModalLabel"
        aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="videoModalLabel"><?php echo e($videoTutorial->title ?? ''); ?></h5>
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </div>
                <div class="modal-body">
                    <iframe id="youtube-video" width="100%" height="315" src="" frameborder="0"
                        allowfullscreen></iframe>
                </div>
            </div>
        </div>
    </div>

    
    <div class="modal fade" id="send-template-bulk" tabindex="-1" aria-labelledby="editModal" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <form type="POST" action="<?php echo e(route('user.chatbot.store')); ?>" class="ajaxform_instant_reload">
                    <?php echo csrf_field(); ?>
                    <div class="modal-header">
                        <h5 class="modal-title" id="exampleModalLabel"><?php echo e(__('Create Reply')); ?></h5>
                        <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                            <span aria-hidden="true">&times;</span>
                        </button>
                    </div>
                    <div class="modal-body">
                        <div class="form-group">
                            <label><?php echo e(__('Select Device')); ?></label>
                            <select class="form-control" name="c_device" id="c_device" data-toggle="select">
                                <option disabled selected><?php echo e(__('Select Device')); ?></option>
                                <?php $__currentLoopData = $devices; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $device): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <option value="<?php echo e($device['id']); ?>"><?php echo e($device['name'] . ' - ' . $device['phone']); ?>

                                    </option>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </select>
                        </div>
                        <div class="form-group">
                            <label><?php echo e(__('Enter keywords (separate multiple keywords with commas)')); ?></label>
                            <input type="text" name="keyword" class="form-control" name="keyword" maxlength="50">
                        </div>
                        <div class="form-group" style="display: none;">
                            <label><?php echo e(__('Keyword Match Type')); ?></label>
                            <select class="form-control" name="match_type" data-toggle="select">
                                <option value="equal" selected><?php echo e(__('Whole Words')); ?></option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label><?php echo e(__('Template')); ?></label>
                            <select class="form-control" id="template" name="template" data-toggle="select">
                                <option value=""><?php echo e(__('-- Select Template --')); ?></option>
                                
                            </select>
                        </div>

                        <div class="row">
                            <div class="form-group col-md-8">
                                <label><?php echo e(__('Assign Agent')); ?></label>
                                <select class="form-control assign_agents" id="agent_id" name="agent_id[]"
                                    multiple="">
                                    <option disabled><?php echo e(__('-- Select Agent --')); ?></option>
                                    <?php $__currentLoopData = $agentData; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $agnt): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                        <option value="<?php echo e($agnt['id']); ?>"><?php echo e($agnt['name']); ?>

                                        </option>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                </select>
                            </div>
                            <div class="form-group col-md-4">
                                <label><?php echo e(__('Assign Rule')); ?></label>
                                <select class="form-control" id="assign_rule" name="assign_rule" data-toggle="select">
                                    <option value="assign_all"><?php echo e(__('Assign All')); ?></option>
                                    <option value="rotation_assign"><?php echo e(__('Rotation Assign')); ?></option>
                                </select>
                            </div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary"
                            data-dismiss="modal"><?php echo e(__('Close')); ?></button>
                        <button type="submit" class="btn btn-primary submit-btn"><?php echo e(__('Create Now')); ?></button>
                    </div>
                </form>
            </div>
        </div>
    </div>
    

    
    <div class="modal fade" id="editModal" tabindex="-1" aria-labelledby="editModal" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <form type="POST" id="edit_rep_form" action="" class="ajaxform_instant_reload edit-reply-form">
                    <?php echo csrf_field(); ?>
                    <?php echo method_field('PUT'); ?>
                    <div class="modal-header">
                        <h5 class="modal-title" id="exampleModalLabel"><?php echo e(__('Edit Reply')); ?></h5>
                        <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                            <span aria-hidden="true">&times;</span>
                        </button>
                    </div>
                    <div class="modal-body">
                        <div class="form-group">
                            <label><?php echo e(__('Select Device')); ?></label>
                            <select class="form-control" name="deviceedit" id="deviceedit">
                                <option disabled><?php echo e(__('Select Device')); ?></option>
                                <?php $__currentLoopData = $devices; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $device): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <option value="<?php echo e($device['id']); ?>"><?php echo e($device['name'] . ' - ' . $device['phone']); ?>

                                    </option>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </select>
                        </div>
                        <div class="form-group">
                            <label><?php echo e(__('Enter keywords (separate multiple keywords with commas)')); ?></label>
                            <input type="text" name="keywordedit" class="form-control" id="keywordedit"
                                required="" maxlength="50">
                        </div>
                        <div class="form-group">
                            <label><?php echo e(__('Template')); ?></label>
                            <select class="form-control" id="templateedit" name="templateedit" data-toggle="select">
                                <option value=""><?php echo e(__('-- Select Template --')); ?></option>
                                
                            </select>
                        </div>

                        <div class="row">
                            <div class="form-group col-md-8">
                                <label><?php echo e(__('Assign Agent')); ?></label>
                                <select class="form-control assign_agents" id="agent_id_edit" name="agent_id_edit[]"
                                    multiple="">
                                    <option disabled><?php echo e(__('-- Select Agent --')); ?></option>
                                    <?php $__currentLoopData = $agentData; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $agnt): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                        <option value="<?php echo e($agnt['id']); ?>"><?php echo e($agnt['name']); ?>

                                        </option>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                </select>
                            </div>
                            <div class="form-group col-md-4">
                                <label><?php echo e(__('Assign Rule')); ?></label>
                                <select class="form-control" id="assign_rule_edit" name="assign_rule_edit"
                                    data-toggle="select">
                                    <option value="">Select Rule</option>
                                    <option value="assign_all"><?php echo e(__('Assign All')); ?></option>
                                    <option value="rotation_assign"><?php echo e(__('Rotation Assign')); ?></option>
                                </select>
                            </div>
                        </div>
                        <div class="form-group">
                            <label for="status">Status</label>
                            <select class="form-control" name="statusedit" id="statusedit">
                                <option value="1">Enabled</option>
                                <option value="0">Disabled</option>
                            </select>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary"
                            data-dismiss="modal"><?php echo e(__('Close')); ?></button>
                        <button type="submit" class="btn btn-primary submit-btn"><?php echo e(__('Update Reply')); ?></button>
                    </div>
                </form>
            </div>
        </div>
    </div>
    
<?php $__env->stopSection(); ?>
<?php $__env->startPush('js'); ?>
    <script src="<?php echo e(asset('assets/vendor/select2/dist/js/select2.min.js')); ?>"></script>
    <script src="<?php echo e(asset('assets/js/pages/user/chatbot.js')); ?>"></script>
    <script src="https://woody180.github.io/vanilla-javascript-emoji-picker/vanillaEmojiPicker.js"></script>
    <script type="text/javascript" src="<?php echo e(asset('assets/js/pages/bulk/template.js')); ?>"></script>

    <script>
        $(document).ready(function() {
            function selectSingleDevice() {
                var deviceDropdown = $("#c_device");
                var optionCount = deviceDropdown.children("option").length;

                if (optionCount == 2) {
                    deviceDropdown.children("option").eq(1).prop('selected', true);
                    deviceDropdown.trigger('change');
                }
            }
            //selectSingleDevice();

            $('#c_device').on('change', function() {
                var selectedDevice = $(this).val();
                $('#template').empty().append(
                    '<option value="" >-- Select Template --</option>');

                if (!selectedDevice) return;

                $.ajax({
                    url: "<?php echo e(route('user.get_templates', ':device_id')); ?>".replace(':device_id',
                        selectedDevice),
                    type: 'GET',
                    success: function(templates) {
                        $.each(templates, function(index, value) {
                            if (value.status == "1") {
                                $('#template').append("<option value='" + value.id +
                                    "'>" + value.title + " -- " + value.type +
                                    "</option>");
                            }
                        });
                    },
                    error: function(xhr) {
                        console.error('AJAX error:', xhr.responseText);
                    }
                });
            });

            selectSingleDevice();
        });
    </script>

    <script>
        $(".assign_agents").select2({
            placeholder: "<?php echo e(__('Select Agent')); ?>",
            allowClear: true
        });
    </script>

    <script>
        $(document).ready(function() {
            $('#editModal').on('show.bs.modal', function(event) {
                var button = $(event.relatedTarget);
                var action = button.data('action');
                var templateedit = button.data('templateid');
                var replyedit = button.data('reply');
                var match_type_edit = button.data('matchtype');

                var keywordedit = button.data('keyword');
                var deviceedit = button.data('device');
                var statusedit = button.data('status');
                var agentjson = button.data('agentjson');

                // Set the form action
                $('#edit_rep_form').attr('action', action);

                $('#deviceedit').on('change', function() {
                    var selectedDevice = $(this).val();
                    $('#templateedit').empty().append(
                        '<option value="" >-- Select Template --</option>');

                    if (!selectedDevice) return;

                    $.ajax({
                        url: "<?php echo e(route('user.get_templates', ':device_id')); ?>".replace(
                            ':device_id',
                            selectedDevice),
                        type: 'GET',
                        success: function(templates) {
                            $.each(templates, function(index, value) {
                                if (value.status == "1") {
                                    $('#templateedit').append(
                                        "<option value='" + value.id +
                                        "'>" + value.title + " -- " + value
                                        .type +
                                        "</option>");
                                }
                            });
                            $('#templateedit').val(templateedit);
                        },
                        error: function(xhr) {
                            console.error('AJAX error:', xhr.responseText);
                        }
                    });
                });

                $('#deviceedit').val(deviceedit).trigger('change');

                // Set the template value
                // $('#templateedit').val(templateedit).trigger('change');

                // Set the reply value
                $('#replyedit').val(replyedit);

                // Set the match type value
                $('#match_type_edit').val(match_type_edit);

                // Set the reply type value
                // $('#reply_type_edit').val(reply_type_edit);

                // Set the keyword value
                $('#keywordedit').val(keywordedit);

                // Set the device value
                $('#deviceedit').val(deviceedit);

                // Set the status value
                $('#statusedit').val(statusedit);

                // Set the agent json value
                $('#agent_id_edit').val(agentjson).trigger('change');
            });
        });
    </script>
    
    <script>
        new EmojiPicker({
            trigger: [{
                selector: '.emojipick',
                insertInto: ['.one'] // '.selector' can be used without array
            }],
            closeButton: true,
            //specialButtons: green
        });

        function insertTag(textareaId, openTag, closeTag) {
            var textarea = document.getElementById(textareaId);
            var startPos = textarea.selectionStart;
            var endPos = textarea.selectionEnd;
            var selectedText = textarea.value.substring(startPos, endPos);
            var newText = openTag + selectedText + closeTag;
            textarea.value = textarea.value.substring(0, startPos) + newText + textarea.value.substring(endPos, textarea
                .value.length);
            textarea.focus();
            textarea.setSelectionRange(endPos + openTag.length + closeTag.length, endPos + openTag.length + closeTag
                .length);
        }
    </script>

    <script>
        const tutorial = <?php echo json_encode($videoTutorial, 15, 512) ?>;

        // Function to extract video ID from YouTube URL
        function getVideoId(url) {
            const regex =
                /(?:https?:\/\/)?(?:www\.)?(?:youtu\.be\/|(?:www\.)?youtube\.com\/(?:watch\?v=|embed\/|v\/|.+\?v=))([^&]+)/;
            const matches = url.match(regex);
            return matches ? matches[1] : null;
        }

        // Event listener for the modal opening
        $('#videoModal').on('show.bs.modal', function() {
            const videoId = getVideoId(tutorial.value); // Extract video ID from the tutorial URL
            if (videoId) {
                const videoUrl = `https://www.youtube.com/embed/${videoId}`;

                // Set the video URL to the iframe
                document.getElementById('youtube-video').src = videoUrl;
            } else {
                console.error('Invalid YouTube URL');
            }
        });

        // Clear the video source when the modal is hidden
        $('#videoModal').on('hidden.bs.modal', function() {
            document.getElementById('youtube-video').src = '';
        });
    </script>
<?php $__env->stopPush(); ?>

<?php echo $__env->make('layouts.main.app', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\Users\<USER>\OneDrive\Vishal\xampp\htdocs\waba_panelv2\resources\views/user/chatbot/index.blade.php ENDPATH**/ ?>