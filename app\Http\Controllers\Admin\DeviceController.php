<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\App;
use Illuminate\Http\Request;
use App\Models\Device;
use App\Models\Greetings;
use App\Models\Reply;
use App\Models\Task;
use App\Models\User;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Http;
use App\Traits\Notifications;
use Exception;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\File;
use Illuminate\Validation\Rule;
use Illuminate\Support\Facades\Log;

class DeviceController extends Controller
{
    use Notifications;

    public function __construct()
    {
        $this->middleware('permission:device');
    }

    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index(Request $request)
    {
        $devices = Device::query();

        if (!empty($request->search)) {
            if ($request->type == 'email') {
                $devices = $devices->whereHas('user', function ($q) use ($request) {
                    return $q->whereRaw('LOWER(email) = ?', [strtolower($request->search)]);
                });
            } else {
                $devices->whereRaw('LOWER(' . $request->type . ') LIKE ?', ['%' . strtolower($request->search) . '%']);
            }
        }

        $login_id = Auth::id();
        if ($login_id == 1) {
            $is_subadmin = false;
        } else {
            $is_subadmin = true;
        }
        $child_users = User::where('status', 1)->where('will_expire', '>', now())
            ->when($is_subadmin, function ($query) use ($login_id) {
                return $query->where('reseller_id', $login_id);
            })
            ->pluck('id');

        $devices = $devices->withCount('smstransaction')->with('user')->latest()->paginate(30);
        $type = $request->type ?? '';

        $totalDevices = Device::count();
        $totalActiveDevices = Device::where('status', 1)->count();
        $totalInactiveDevices = Device::where('status', 0)->count();

        return view('admin.devices.index', compact('devices', 'request', 'type', 'totalDevices', 'totalActiveDevices', 'totalInactiveDevices'));
    }

    public function create()
    {
        $login_id = Auth::id();
        if ($login_id == 1) {
            $is_subadmin = false;
        } else {
            $is_subadmin = true;
        }

        // $customers = User::query();
        $customers = User::where('role', 'user')
            ->when($is_subadmin, function ($query) use ($login_id) {
                return $query->where('reseller_id', $login_id);
            })
            ->latest()->get();

        return view('admin.devices.create', compact('customers'));
    }

    public function store(Request $request)
    {
        $validatedData = $request->validate([
            'customer' => 'required',
            'name' => 'required',
            'phone' => 'nullable|unique:devices,phone',
            'token' => 'nullable',
        ]);

        $device = new Device;
        $device->user_id = $request->customer;
        $device->name = $request->name;
        $device->phone = $request->phone ?? null;
        $device->token = $request->token ?? null;
        $device->status = 1;
        $device->save();

        if ($device) {
            return response()->json([
                'redirect' => route('admin.devices.index'),
                'message' => __('Device Created successfully.')
            ], 200);
        } else {
            return response()->json([
                'message' => __('Something went wrong')
            ], 422);
        }
    }

    public function edit($id)
    {
        $login_id = Auth::id();
        if ($login_id == 1) {
            $is_subadmin = false;
        } else {
            $is_subadmin = true;
        }

        $customers = User::where('role', 'user')
            ->when($is_subadmin, function ($query) use ($login_id) {
                return $query->where('reseller_id', $login_id);
            })
            ->latest()->get();

        $device = Device::findOrFail($id);
        return view('admin.devices.edit', compact('device', 'customers'));
    }

    public function update(Request $request, $id)
    {
        $validatedData = $request->validate([
            'customer' => 'required',
            'name' => 'required',
            'phone' => 'nullable|unique:devices,phone,' . $id,
            'token' => 'nullable',
        ]);

        $device = Device::findOrFail($id);
        $device->user_id = $request->customer;
        $device->name = $request->name;
        $device->phone = $request->phone ?? null;
        $device->token = $request->token ?? null;
        $device->status = $request->status;
        $device->save();

        if ($device) {
            return response()->json([
                'redirect' => route('admin.devices.index'),
                'message' => __('Device updated successfully.')
            ], 200);
        } else {
            return response()->json([
                'message' => __('Something went wrong')
            ], 422);
        }
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function destroy($id)
    {
        $device = Device::findorFail($id);

        $title = 'Your a device was removed by admin';
        $notification['user_id'] = $device->user_id;
        $notification['title'] = $title;
        $notification['url'] = '/user/device';

        $this->createNotification($notification);

        DB::beginTransaction();

        try {
            if ($device->status == 1) {
                Http::delete(env('APP_URL') . '/sessions/delete/device_' . $device->id);
            }

            // delete from replies table
            Reply::where('device_id', $device->id)->delete();

            // delete from apps table
            App::where('device_id', $device->id)->delete();

            //delete from greetigs table with media if have
            $greetings = Greetings::where('device_id', $device->id)->get();

            foreach ($greetings as $greeting) {
                // Delete birthday media if it exists.
                if ($greeting->birth_day_media) {
                    $birthDayMediaPath = public_path('uploads/greetings/' . $greeting->birth_day_media);
                    if (File::exists($birthDayMediaPath)) {
                        File::delete($birthDayMediaPath);
                    }
                }

                // Delete anniversary media if it exists.
                if ($greeting->anni_day_media) {
                    $anniDayMediaPath = public_path('uploads/greetings/' . $greeting->anni_day_media);
                    if (File::exists($anniDayMediaPath)) {
                        File::delete($anniDayMediaPath);
                    }
                }
                $greeting->delete();
            }

            // delete from messages table with media file if have.
            $messages = DB::table('messages')
                ->where('device_phone', $device->phone)
                ->get();

            foreach ($messages as $message) {
                if ($message->media) {
                    $mediaPath = public_path('uploads/incomming_media/' . $message->media);
                    if (File::exists($mediaPath)) {
                        File::delete($mediaPath);
                    }
                }
            }

            DB::table('messages')
                ->where('device_phone', $device->phone)
                ->delete();

            // delete from task table
            $tasks = Task::where('device_id', $device->id)->get();

            foreach ($tasks as $task) {
                if ($task->task_url) {
                    // Convert the full URL to a relative file path.
                    // url('/') returns your base URL (e.g., http://192.168.0.101/waba_panelv2/public)
                    $relativePath = str_replace(url('/'), '', $task->task_url);
                    $filePath = public_path($relativePath);

                    if (File::exists($filePath)) {
                        File::delete($filePath);
                    }
                }
            }

            Task::where('device_id', $device->id)->delete();

            $device->delete();

            DB::commit();

            return response()->json([
                'redirect' => route('admin.devices.index'),
                'message' => __('Device Removed successfully.')
            ]);
        } catch (Exception $e) {
            DB::rollBack();
            return response()->json(['error' => $e->getMessage()], 500);
        }
    }

    public function updateAboutUs(Request $request)
    {
        $deviceId = $request->device;
        $device = Device::where('id', $deviceId)->first();

        if (!$device) {
            return response()->json([
                'message' => __('Device not found')
            ], 404);
        }

        // Call the updateAboutUs helper function
        $result = updateAboutUs($device->phoneid, $device->token, $request->about);

        if ($result['success']) {
            // Log the successful update
            return response()->json([
                'success' => true,
                'redirect' => route('admin.devices.index'),
                'message' => __('About Us updated successfully')
            ]);
        }

        // Log the failed attempt
        Log::warning('WhatsApp About Us update failed', [
            'device_id' => $device->id,
            'error' => $result['error'] ?? null,
            'status' => $result['status'] ?? null
        ]);

        return response()->json([
            'success' => false,
            'message' => __('Failed to update About Us'),
            'error' => $result['error'] ?? null
        ], $result['status'] ?? 400);
    }
}
