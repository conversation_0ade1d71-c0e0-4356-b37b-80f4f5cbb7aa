
<?php $__env->startSection('head'); ?>
    <?php echo $__env->make('layouts.main.headersection', [
        'title' => __('Blacklist Contact List'),
    ], \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
<?php $__env->stopSection(); ?>
<?php $__env->startPush('topcss'); ?>
    <link rel="stylesheet" type="text/css" href="<?php echo e(asset('assets/vendor/select2/dist/css/select2.min.css')); ?>">
<?php $__env->stopPush(); ?>
<?php $__env->startSection('content'); ?>
    <div class="row justify-content-center">
        <div class="col-12">
            <div class="row d-flex justify-content-between flex-wrap">
                <div class="col">
                    <div class="card card-stats">
                        <div class="card-body">
                            <div class="row">
                                <div class="col">
                                    <span class="h2 font-weight-bold mb-0 total-transfers" id="total-device">
                                        <?php echo e($total_contacts); ?>

                                    </span>
                                </div>
                                <div class="col-auto">
                                    <div class="icon icon-shape bg-gradient-primary text-white rounded-circle shadow">
                                        <i class="fi  fi-rs-address-book mt-2"></i>
                                    </div>
                                </div>
                            </div>
                            <p class="mt-3 mb-0 text-sm">
                            </p>
                            <h5 class="card-title  text-muted mb-0"><?php echo e(__('Total Contacts')); ?></h5>
                            <p></p>
                        </div>
                    </div>
                </div>
                <div class="col">
                </div>
            </div>
            <?php if(count($allContacts ?? []) > 0): ?>
                <div class="card">
                    <div class="card-header border-0">
                        <h3 class="mb-0"><?php echo e(__('Block Contacts')); ?></h3>
                        
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-sm-12 table-responsive">
                                <table class="table col-12">
                                    <thead>
                                        <tr>
                                            <th class="col-3"><?php echo e(__('Device')); ?></th>
                                            <th class="col-3"><?php echo e(__('Blacklist Number')); ?></th>
                                            <th class="col-2 text-right"><?php echo e(__('Action')); ?></th>
                                        </tr>
                                    </thead>
                                    <tbody class="tbody">
                                        <?php $__currentLoopData = $allContacts ?? []; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $contact): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                            <tr>
                                                <td>
                                                    <?php echo e($contact['name']); ?><br>
                                                    <small> (<?php echo e($contact['phone']); ?>)</small>
                                                </td>
                                                <td>
                                                    <?php echo e($contact['number']); ?>

                                                </td>
                                                <td>
                                                    <div class="btn-group mb-2 float-right">
                                                        <button class="btn btn-neutral btn-sm dropdown-toggle"
                                                            type="button" data-toggle="dropdown" aria-haspopup="true"
                                                            aria-expanded="false">
                                                            <?php echo e(__('Action')); ?>

                                                        </button>
                                                        <div class="dropdown-menu">
                                                            <input type="hidden" id="tagrecivernumber"
                                                                value="<?php echo e($contact['number']); ?>">
                                                            <input type="hidden" id="uuid"
                                                                value="<?php echo e($contact['uuid']); ?>">
                                                            <a class="dropdown-item has-icon" href="javascript:void(0)"
                                                                onclick="unblockNumber()"><i
                                                                    class="fas fa-trash"></i><?php echo e(__('Unblock Number')); ?></a>
                                                        </div>
                                                    </div>
                                                </td>
                                            </tr>
                                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                    </tbody>
                                </table>
                                
                            </div>
                        </div>
                    </div>
                </div>
            <?php else: ?>
                <div class="alert  bg-gradient-primary text-white"><span
                        class="text-left"><?php echo e(__('Opps There Is No Contact Found....')); ?></span></div>
            <?php endif; ?>
        </div>
    </div>
<?php $__env->stopSection(); ?>
<?php $__env->startPush('topjs'); ?>
    <script src="<?php echo e(asset('assets/vendor/select2/dist/js/select2.min.js')); ?>"></script>
    <script>
        // click to unblock number
        function unblockNumber() {
            console.log('unblockNumber');

            // Get the value of the hidden input
            var number = document.getElementById('tagrecivernumber').value;
            var uuid = document.getElementById('uuid').value;

            // Check if the number exists
            if (number) {

                var data = {
                    number: number,
                    uuid: uuid,
                    status: 'unblock'
                };

                $.ajax({
                    url: "<?php echo e(route('user.blockUnblockUser')); ?>",
                    type: "POST",
                    data: data,
                    dataType: "JSON",
                    headers: {
                        'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
                    },
                    success: function(data) {
                        console.log('unblockNumber data', data);

                        if (data.success) {
                            alert(data.message);

                            // Refresh the page after 1 second
                            setTimeout(function() {
                                location.reload();
                            }, 1000);
                            // location.reload();

                        } else {
                            console.error("data not save");
                        }
                    },
                    error: function(jqXHR, textStatus, errorThrown) {
                        console.error("Error:", textStatus, errorThrown);
                        // alertify.error('Error adding / update data');
                    }
                });
            } else {
                //alert("Select a contact to dial.");
                //console.log("No mobile number available.");
            }
        }
    </script>
<?php $__env->stopPush(); ?>

<?php echo $__env->make('layouts.main.app', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\Users\<USER>\OneDrive\Vishal\xampp\htdocs\waba_panelv2\resources\views/user/contact/blacklists.blade.php ENDPATH**/ ?>