@extends('layouts.main.app')
@section('head')
    @include('layouts.main.headersection', [
        'title' => __('Link Device'),
        'buttons' => [
            [
                'name' => __('Back'),
                'url' => route('admin.devices.index'),
            ],
        ],
    ])
@endsection
@section('content')
    <div class="row">
        <div class="col-lg-5 mt-5">
            <strong>{{ __('Link Device') }}</strong>
            <p>{{ __('add device') }}</p>
        </div>
        <div class="col-lg-7 mt-5">
            <div class="card">
                <form method="post" action="{{ route('admin.devices.store') }}" class="ajaxform_instant_reload"
                    autocomplete="off">
                    @csrf
                    <div class="card-body">
                        <div class="pt-20">
                            <div class="form-group">
                                <label for="name">Customer</label>
                                <select class="form-control" name="customer">
                                    <option value="">{{ __('Select User') }}</option>
                                    @foreach ($customers as $customer)
                                        <option value="{{ $customer['id'] }}">
                                            {{ $customer['name'] . ' - ' . $customer['email'] }}
                                        </option>
                                    @endforeach
                                </select>
                            </div>
                            <div class="form-group">
                                <label for="name">Enter Name</label>
                                <input type="text" placeholder="Enter Name" name="name" class="form-control"
                                    id="name" required>
                            </div>
                            <div class="form-group">
                                <label for="phone">Registered Number with Country Code <small>(optional)</small></label>
                                <input type="text" placeholder="919876543210" name="phone" class="form-control"
                                    id="phone">
                            </div>
                            <div class="form-group">
                                <label for="token">Agent Id</label>
                                <input type="text" placeholder="Your Agent ID" name="token" class="form-control"
                                    id="token">
                            </div>
                        </div>
                    </div>
                    <div class="card-footer">
                        <div class="btn-publish">
                            <button type="submit" class="btn btn-neutral submit-button"><i class="fa fa-save"></i>
                                {{ __('Save') }}</button>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    @endsection
