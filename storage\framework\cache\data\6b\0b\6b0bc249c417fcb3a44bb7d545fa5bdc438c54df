1748935881O:17:"App\Models\Option":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:7:"options";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:4:{s:2:"id";i:3;s:3:"key";s:13:"base_currency";s:5:"value";s:43:"{"name":"USD","icon":"$","position":"left"}";s:4:"lang";s:2:"en";}s:11:" * original";a:4:{s:2:"id";i:3;s:3:"key";s:13:"base_currency";s:5:"value";s:43:"{"name":"USD","icon":"$","position":"left"}";s:4:"lang";s:2:"en";}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:0;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:1:{i:0;s:1:"*";}}