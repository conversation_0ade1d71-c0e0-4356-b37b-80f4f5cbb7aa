@extends('layouts.main.app')
@section('head')
    @push('css')
        <link rel="stylesheet" href="{{ asset('assets/plugins/bootstrap-iconpicker/css/bootstrap-iconpicker.min.css') }}" />
    @endpush
    @include('layouts.main.headersection', [
        'title' => __('Edit Device'),
        'buttons' => [
            [
                'name' => __('Back'),
                'url' => route('admin.devices.index'),
            ],
        ],
    ])
@endsection
@section('content')
    <div class="row ">
        <div class="col-lg-5 mt-5">
            <strong>{{ __('Device') }}</strong>
            <p>{{ __('Edit Device') }}</p>
        </div>
        <div class="col-lg-7 mt-5">
            <form class="ajaxform_instant_reload" action="{{ route('admin.devices.update', $device->id) }}"
                enctype="multipart/form-data">
                @csrf
                @method('PUT')
                <div class="card">
                    <div class="card-body">
                        <div class="from-group row mt-2">
                            <label class="col-lg-12">{{ __('Customer') }}</label>
                            <div class="col-lg-12">
                                <select class="form-control" name="customer">
                                    <option value="">{{ __('Select User') }}</option>
                                    @foreach ($customers as $customer)
                                        <option value="{{ $customer['id'] }}"
                                            {{ $device->user_id == $customer['id'] ? 'selected' : '' }}>
                                            {{ $customer['name'] . ' - ' . $customer['email'] }}
                                        </option>
                                    @endforeach
                                </select>
                            </div>
                        </div>
                        <div class="from-group row mt-2">
                            <label class="col-lg-12">{{ __('Enter Name') }}</label>
                            <div class="col-lg-12">
                                <input type="text" name="name" required="" class="form-control"
                                    value="{{ $device->name }}">
                            </div>
                        </div>
                        <div class="from-group row mt-2">
                            <label class="col-lg-12">{{ __('Phone') }} <small>(optional)</small></label>
                            <div class="col-lg-12">
                                <input type="text" name="phone" class="form-control"
                                    value="{{ $device->phone }}">
                            </div>
                        </div>
                        <div class="from-group row mt-2">
                            <label class="col-lg-12">{{ __('Agent ID') }}</label>
                            <div class="col-lg-12">
                                <input type="text" name="token" required="" class="form-control"
                                    value="{{ $device->token }}">
                            </div>
                        </div>
                        <div class="from-group row mt-2">
                            <label class="col-lg-12">{{ __('Status') }}</label>
                            <div class="col-lg-12">
                                <select class="form-control" name="status">
                                    <option value="1" {{ $device->status == 1 ? 'selected' : '' }}>Active</option>
                                    <option value="0" {{ $device->status == 0 ? 'selected' : '' }}>Inactive</option>
                                </select>
                            </div>
                        </div>
                        <div class="from-group row mt-3">
                            <div class="col-lg-12">
                                <button class="btn btn-neutral submit-button btn-sm float-left">
                                    {{ __('Update') }}</button>
                            </div>
                        </div>
                    </div>
                </div>
            </form>
        </div>
    </div>
@endsection
