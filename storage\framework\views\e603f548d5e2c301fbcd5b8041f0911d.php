
<?php $__env->startSection('head'); ?>
    <?php $__env->startPush('css'); ?>
        <link rel="stylesheet" href="<?php echo e(asset('assets/plugins/bootstrap-iconpicker/css/bootstrap-iconpicker.min.css')); ?>" />
    <?php $__env->stopPush(); ?>
    <?php echo $__env->make('layouts.main.headersection', [
        'title' => __('Edit Device'),
        'buttons' => [
            [
                'name' => __('Back'),
                'url' => route('admin.devices.index'),
            ],
        ],
    ], \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
<?php $__env->stopSection(); ?>
<?php $__env->startSection('content'); ?>
    <div class="row ">
        <div class="col-lg-5 mt-5">
            <strong><?php echo e(__('Device')); ?></strong>
            <p><?php echo e(__('Edit Device')); ?></p>
        </div>
        <div class="col-lg-7 mt-5">
            <form class="ajaxform_instant_reload" action="<?php echo e(route('admin.devices.update', $device->id)); ?>"
                enctype="multipart/form-data">
                <?php echo csrf_field(); ?>
                <?php echo method_field('PUT'); ?>
                <div class="card">
                    <div class="card-body">
                        <div class="from-group row mt-2">
                            <label class="col-lg-12"><?php echo e(__('Customer')); ?></label>
                            <div class="col-lg-12">
                                <select class="form-control" name="customer">
                                    <option value=""><?php echo e(__('Select User')); ?></option>
                                    <?php $__currentLoopData = $customers; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $customer): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                        <option value="<?php echo e($customer['id']); ?>"
                                            <?php echo e($device->user_id == $customer['id'] ? 'selected' : ''); ?>>
                                            <?php echo e($customer['name'] . ' - ' . $customer['email']); ?>

                                        </option>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                </select>
                            </div>
                        </div>
                        <div class="from-group row mt-2">
                            <label class="col-lg-12"><?php echo e(__('Enter Name')); ?></label>
                            <div class="col-lg-12">
                                <input type="text" name="name" required="" class="form-control"
                                    value="<?php echo e($device->name); ?>">
                            </div>
                        </div>
                        <div class="from-group row mt-2">
                            <label class="col-lg-12"><?php echo e(__('Phone')); ?> <small>(optional)</small></label>
                            <div class="col-lg-12">
                                <input type="text" name="phone" class="form-control"
                                    value="<?php echo e($device->phone); ?>">
                            </div>
                        </div>
                        <div class="from-group row mt-2">
                            <label class="col-lg-12"><?php echo e(__('Agent ID')); ?></label>
                            <div class="col-lg-12">
                                <input type="text" name="token" required="" class="form-control"
                                    value="<?php echo e($device->token); ?>">
                            </div>
                        </div>
                        <div class="from-group row mt-2">
                            <label class="col-lg-12"><?php echo e(__('Status')); ?></label>
                            <div class="col-lg-12">
                                <select class="form-control" name="status">
                                    <option value="1" <?php echo e($device->status == 1 ? 'selected' : ''); ?>>Active</option>
                                    <option value="0" <?php echo e($device->status == 0 ? 'selected' : ''); ?>>Inactive</option>
                                </select>
                            </div>
                        </div>
                        <div class="from-group row mt-3">
                            <div class="col-lg-12">
                                <button class="btn btn-neutral submit-button btn-sm float-left">
                                    <?php echo e(__('Update')); ?></button>
                            </div>
                        </div>
                    </div>
                </div>
            </form>
        </div>
    </div>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.main.app', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\Users\<USER>\OneDrive\Vishal\xampp\htdocs\waba_panelv2\resources\views/admin/devices/edit.blade.php ENDPATH**/ ?>