<nav class="navbar navbar-top navbar-expand navbar-light bg-secondary border-bottom">
    <div class="container-fluid">
        <div class="collapse navbar-collapse" id="navbarSupportedContent">
            <!-- Search form -->

            <!-- Navbar links -->
            <ul class="navbar-nav align-items-center ml-md-auto">
                <li class="nav-item d-xl-none">
                    <!-- Sidenav toggler -->
                    <div class="pr-3 sidenav-toggler sidenav-toggler-light" data-action="sidenav-pin"
                        data-target="#sidenav-main">
                        <div class="sidenav-toggler-inner">
                            <i class="sidenav-toggler-line"></i>
                            <i class="sidenav-toggler-line"></i>
                            <i class="sidenav-toggler-line"></i>
                        </div>
                    </div>
                </li>
                <?php if(Request::is('user/*')): ?>
                    <li class="nav-item dropdown notifications-icon none notifications-area">
                        <a class="nav-link" href="#" role="button" data-toggle="dropdown" aria-haspopup="true"
                            aria-expanded="false">
                            <i class="fi fi-rs-bell"></i>
                        </a>
                        <div class="dropdown-menu dropdown-menu-xl dropdown-menu-right py-0 overflow-hidden">
                            <!-- Dropdown header -->
                            <div class="px-3 py-3">
                                <h6 class="text-sm text-muted m-0"><?php echo e(__('You have')); ?> <strong
                                        class="text-primary notification-count">0</strong> <?php echo e(__('notifications.')); ?>

                                </h6>
                            </div>
                            <!-- List group -->
                            <div class="list-group list-group-flush notifications-list">


                            </div>
                            <!-- View all -->

                        </div>
                    </li>

                    
                <?php endif; ?>
            </ul>

            <ul class="navbar-nav align-items-center ml-auto ml-md-0">
                <li class="nav-item dropdown">
                    <a class="nav-link pr-0" href="#" role="button" data-toggle="dropdown" aria-haspopup="true"
                        aria-expanded="false">
                        <div class="media align-items-center">
                            <span class="avatar avatar-sm rounded-circle">
                                <img alt="Image placeholder"
                                    src="<?php echo e(Auth::user()->avatar == null ? 'https://ui-avatars.com/api/?name=' . Auth::user()->name : asset(Auth::user()->avatar)); ?>">
                            </span>
                            <div class="media-body ml-2 d-none d-lg-block">
                                <span class="mb-0 text-sm  font-weight-bold"><?php echo e(Auth::user()->name); ?></span>
                            </div>
                        </div>
                    </a>
                    <div class="dropdown-menu dropdown-menu-right">
                        <div class="dropdown-header noti-title">
                            <h6 class="text-overflow m-0"><?php echo e(__('Welcome!')); ?></h6>
                        </div>
                        <?php if(Request::is('agent/*')): ?>
                            <a href="<?php echo e(route('agent.dashboard.index')); ?>" class="dropdown-item">
                                <i class="fi fi-rs-devices"></i>
                                <span><?php echo e(__('Devices')); ?></span>
                            </a>
                        <?php endif; ?>
                        <a href="<?php echo e(Request::is('user/*') ? url('/user/profile') : (Request::is('agent/*') ? url('/agent/profile') : url('/admin/profile'))); ?>"
                            class="dropdown-item">
                            <i class="ni ni-single-02"></i>
                            <span><?php echo e(__('My profile')); ?></span>
                        </a>
                        
                        <a href="<?php echo e(Request::is('user/*') ? url('/user/support') : (Request::is('agent/*') ? url('/agent/support') : url('/admin/support'))); ?>"
                            class="dropdown-item">
                            <i class="ni ni-support-16"></i>
                            <span><?php echo e(__('Support')); ?></span>
                        </a>
                        <div class="dropdown-divider"></div>
                        <a href="#!" class="dropdown-item logout-button">
                            <i class="ni ni-user-run"></i>
                            <span><?php echo e(__('Logout')); ?></span>
                        </a>
                    </div>
                </li>
            </ul>
        </div>
    </div>
</nav>
<!-- Header -->
<?php /**PATH C:\Users\<USER>\OneDrive\Vishal\xampp\htdocs\waba_panelv2\resources\views/layouts/main/header.blade.php ENDPATH**/ ?>