

<?php $__env->startSection('head'); ?>
    <?php echo $__env->make('layouts.main.headersection', [
        'title' => __('Response'),
        'buttons' => array_filter([
            isset($videoTutorial->value)
                ? [
                    'name' => '<i class="fi-rs-photo-video"></i>&nbsp&nbsp' . __('Video Tutorial'),
                    'url' => '#',
                    'components' => 'data-toggle="modal" data-target="#videoModal"',
                    'is_button' => true,
                ]
                : null,
            [
                'name' => __('Flow Report'),
                'url' => url('user/flow_report'),
            ],
        ]),
    ], \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
<?php $__env->stopSection(); ?>
<?php $__env->startSection('content'); ?>
    <div class="row">
        <!-- Modal for YouTube video -->
        <div class="modal fade" id="videoModal" tabindex="-1" role="dialog" aria-labelledby="videoModalLabel"
            aria-hidden="true">
            <div class="modal-dialog modal-dialog-centered" role="document">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title" id="videoModalLabel"><?php echo e($videoTutorial->title ?? ''); ?></h5>
                        <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                            <span aria-hidden="true">&times;</span>
                        </button>
                    </div>
                    <div class="modal-body">
                        <iframe id="youtube-video" width="100%" height="315" src="" frameborder="0"
                            allowfullscreen></iframe>
                    </div>
                </div>
            </div>
        </div>
        <div class="col">
            <div class="card">
                <!-- Card header -->
                <div class="card-header border-0">
                    <h3 class="mb-0"><?php echo e(__('Response')); ?></h3>
                    <form class="card-header-form">
                        <div class="input-group">
                            <input type="text" class="form-control" style="width: 200px" name="daterange" id="daterange"
                                value="<?php echo e($initialDateRange); ?>">
                            <select class="form-control" id="campaign_device" name="campaign_device" style="width: 200px">
                                <option value=""><?php echo e(__('Select Device')); ?></option>
                                <?php $__currentLoopData = $device_data; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $device): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <option value="<?php echo e($device['id']); ?>"><?php echo e($device['name']); ?>

                                        <?php if(!empty($device['phone'])): ?>
                                            (+<?php echo e($device['phone']); ?>)
                                        <?php endif; ?>
                                    </option>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </select>
                            <button onclick="export_response_data(event)" class="btn btn-sm text-primary submit-button"><i
                                    class="fa fi-rs-file-excel" style="font-size: 2.5em;"></i></button>
                        </div>
                    </form>
                </div>
                <!-- Light table -->
                <div class="table-responsive">
                    <table id="example" class="table table-bordered table-striped table-hover dataTable">
                        <thead>
                            <tr>
                                <th>Device Number</th>
                                <th>Sender Number</th>
                                <th>Sender Name</th>
                                <th>Timestamp</th>
                                <th>Type</th>
                                <th>Message</th>
                                <th>Media</th>
                            </tr>
                        </thead>
                        <tfoot>
                            <tr>
                                <th>Device Number</th>
                                <th>Sender Number</th>
                                <th>Sender Name</th>
                                <th>Timestamp</th>
                                <th>Type</th>
                                <th>Message</th>
                                <th>Media</th>
                            </tr>
                        </tfoot>
                        <tbody>
                        </tbody>
                    </table>
                </div>
                <div class="card-footer py-4">
                    
                </div>
            </div>
        </div>
    </div>
<?php $__env->stopSection(); ?>
<?php $__env->startPush('js'); ?>
    <script>
        const tutorial = <?php echo json_encode($videoTutorial, 15, 512) ?>;

        // Function to extract video ID from YouTube URL
        function getVideoId(url) {
            const regex =
                /(?:https?:\/\/)?(?:www\.)?(?:youtu\.be\/|(?:www\.)?youtube\.com\/(?:watch\?v=|embed\/|v\/|.+\?v=))([^&]+)/;
            const matches = url.match(regex);
            return matches ? matches[1] : null;
        }

        // Event listener for the modal opening
        $('#videoModal').on('show.bs.modal', function() {
            const videoId = getVideoId(tutorial.value); // Extract video ID from the tutorial URL
            if (videoId) {
                const videoUrl = `https://www.youtube.com/embed/${videoId}`;

                // Set the video URL to the iframe
                document.getElementById('youtube-video').src = videoUrl;
            } else {
                console.error('Invalid YouTube URL');
            }
        });

        // Clear the video source when the modal is hidden
        $('#videoModal').on('hidden.bs.modal', function() {
            document.getElementById('youtube-video').src = '';
        });
    </script>
<?php $__env->stopPush(); ?>
<?php $__env->startPush('js'); ?>
    <script type="text/javascript" src="https://code.jquery.com/jquery-3.7.0.js"></script>
    <script type="text/javascript" src="https://cdn.datatables.net/1.13.6/js/jquery.dataTables.min.js"></script>
    <script type="text/javascript" src="https://cdn.datatables.net/1.13.6/js/dataTables.bootstrap5.min.js"></script>
    <script type="text/javascript" src="https://cdn.jsdelivr.net/momentjs/latest/moment.min.js"></script>

    <script type="text/javascript" src="https://cdn.jsdelivr.net/npm/daterangepicker/daterangepicker.min.js"></script>
    <link rel="stylesheet" type="text/css" href="https://cdn.jsdelivr.net/npm/daterangepicker/daterangepicker.css" />
    <link rel="stylesheet" type="text/css" href="https://cdn.datatables.net/1.13.6/css/dataTables.bootstrap5.min.css" />

    <script src="<?php echo e(asset('assets/js/pages/user/report-index.js')); ?>"></script>

    <script type="text/javascript">
        var start_date = moment().format('YYYY-MM-DD');
        var end_date = moment().format('YYYY-MM-DD');
        var select_device = '';

        // date range filter starts
        $(document).ready(function() {
            $(function() {
                var initialDateRange = start_date + ' - ' + end_date;

                $('input[name="daterange"]').daterangepicker({
                    opens: 'left',
                }, function(start, end, label) {
                    start_date = start.format('YYYY-MM-DD');
                    end_date = end.format('YYYY-MM-DD');
                    $('#example').DataTable().ajax.reload();
                });

                // Set the initial date range text
                $('input[name="daterange"]').val(initialDateRange);

                // Initialize DataTable with the initial date range
                $('#example').DataTable().ajax.reload();
            });
            // $('#campaign_device').change(function() {
            //     select_device = $(this).val();
            //     $('#example').DataTable().ajax.reload();
            // });
            $(document).ready(function() {
                // Check the number of options in the select element
                var $deviceSelect = $('#campaign_device');
                var deviceOptions = $deviceSelect.find('option').not(
                    ':first'); // Exclude the "Select Device" option

                if (deviceOptions.length === 1) {
                    // Automatically select the only available device
                    $deviceSelect.val(deviceOptions.val()).change();
                }

                // Attach change event handler
                $deviceSelect.change(function() {
                    select_device = $(this).val();
                    $('#example').DataTable().ajax.reload();
                });
            });
        });
        //data range filter ends

        function export_response_data(event) {
            event.preventDefault();
            // console.log(start_date);
            var downloadUrl = "<?php echo e(route('user.export_response_data')); ?>?start_date=" + start_date + "&end_date=" +
                end_date + "&select_device=" + select_device;
            window.location.href = downloadUrl;
        }

        $(document).ready(function() {


            $('#example').DataTable({
                dom: 'lfrtp',
                deferRender: true,
                searchable: true,
                processing: true,
                orderClasses: true,
                serverSide: true,
                language: {
                    paginate: {
                        next: '&#8594;', // or '→'
                        previous: '&#8592;' // or '←' 
                    }
                },
                order: [
                    [2, "desc"]
                ],

                serverSide: true,
                ajax: {
                    url: "<?php echo e(route('user.get_response_data_filter')); ?>",
                    type: 'POST',
                    dataType: 'JSON',
                    headers: {
                        'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
                    },
                    data: function(d) {
                        // console.log(d);

                        d.start_date = start_date;
                        d.end_date = end_date;
                        d.select_device = select_device;
                    }
                },
                columns: [{
                        data: 'device_phone'
                    },
                    {
                        data: 'sender_no'
                    },
                    {
                        data: 'name'
                    },
                    {
                        data: 'timestamp',
                        render: function(data, type, row, meta) {
                            return moment(data).format('YYYY-MM-DD HH:mm:ss');
                        }
                    },
                    {
                        data: 'type'
                    },
                    {
                        data: 'body'
                    },
                    {
                        data: 'media',
                        render: function(data, type, row, meta) {
                            if (data === null) {
                                return "No Media";
                            }
                            return "<a href='/uploads/incomming_media/" + data +
                                "' target='_blank' rel='noopener noreferrer' class='btn btn-sm btn-outline-primary submit-button waves-float'><i class='fa fa-eye'></i></a>";
                        }
                    },
                ],
            });
        });
    </script>
<?php $__env->stopPush(); ?>

<?php echo $__env->make('layouts.main.app', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\Users\<USER>\OneDrive\Vishal\xampp\htdocs\waba_panelv2\resources\views/user/response/index.blade.php ENDPATH**/ ?>