

<?php $__env->startSection('head'); ?>
    <?php echo $__env->make('layouts.main.headersection', [
        'title' => __('Report'),
        'buttons' => isset($videoTutorial->value)
            ? [
                [
                    'name' => '<i class="fi-rs-photo-video"></i>&nbsp;&nbsp;' . __('Video Tutorial'),
                    'url' => '#',
                    'components' => 'data-toggle="modal" data-target="#videoModal"',
                    'is_button' => true,
                ],
            ]
            : [],
    ], \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
<?php $__env->stopSection(); ?>
<?php $__env->startSection('content'); ?>
    <div class="row" id="test">
        <div class="col-xl-3 col-md-6">
            <div class="card card-stats">
                <!-- Card body -->
                <div class="card-body">
                    <div class="row">
                        <div class="col">
                            <h5 class="card-title text-uppercase text-muted mb-0"><?php echo e(__('Total pending')); ?></h5>
                            <span class="h2 font-weight-bold mb-0"><?php echo e($pending_count); ?></span>
                            <br>
                            <span class="todayTotal">
                                <span class="text-uppercase text-muted mt-1">Today Total - </span>
                                <span class="font-weight-bold"><?php echo e($todayPendingCount ?? 0); ?></span>
                            </span>
                        </div>
                        <div class="col-auto">
                            <div class="icon icon-shape bg-gradient-primary text-white rounded-circle shadow">
                                <i class="fa fa-exclamation"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-xl-3 col-md-6">
            <div class="card card-stats">
                <!-- Card body -->
                <div class="card-body">
                    <div class="row">
                        <div class="col">
                            <h5 class="card-title text-uppercase text-muted mb-0"><?php echo e(__('Total Delievered')); ?></h5>
                            <span class="h2 font-weight-bold mb-0 mt-1"><?php echo e($delivered_count); ?></span>
                            <br>
                            <span class="todayTotal">
                                <span class="text-uppercase text-muted mt-1">Today Total - </span>
                                <span class="font-weight-bold"><?php echo e($todayDelieCount ?? 0); ?></span>
                            </span>
                        </div>
                        <div class="col-auto">
                            <div class="icon icon-shape bg-gradient-primary text-white rounded-circle shadow">
                                <i class="ni ni-spaceship"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-xl-3 col-md-6">
            <div class="card card-stats">
                <!-- Card body -->
                <div class="card-body">
                    <div class="row">
                        <div class="col">
                            <h5 class="card-title text-uppercase text-muted mb-0"><?php echo e(__('Total Read')); ?></h5>
                            <span class="h2 font-weight-bold mb-0"><?php echo e($read_count); ?></span>
                            <br>
                            <span class="todayTotal">
                                <span class="text-uppercase text-muted mt-1">Today Total - </span>
                                <span class="font-weight-bold"><?php echo e($todayReadCount ?? 0); ?></span>
                            </span>
                        </div>
                        <div class="col-auto">
                            <div class="icon icon-shape bg-gradient-primary text-white rounded-circle shadow">
                                <i class="fa fa-check-double"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-xl-3 col-md-6">
            <div class="card card-stats">
                <!-- Card body -->
                <div class="card-body">
                    <div class="row">
                        <div class="col">
                            <h5 class="card-title text-uppercase text-muted mb-0"><?php echo e(__('Total Failed')); ?></h5>
                            <span class="h2 font-weight-bold mb-0"><?php echo e($failed_count); ?></span>
                            <br>
                            <span class="todayTotal">
                                <span class="text-uppercase text-muted mt-1">Today Total - </span>
                                <span class="font-weight-bold"><?php echo e($todayFailCount ?? 0); ?></span>
                            </span>
                        </div>
                        <div class="col-auto">
                            <div class="icon icon-shape bg-gradient-primary text-white rounded-circle shadow">
                                <i class="ni ni-collection"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Modal for YouTube video -->
    <div class="modal fade" id="videoModal" tabindex="-1" role="dialog" aria-labelledby="videoModalLabel"
        aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="videoModalLabel"><?php echo e($videoTutorial->title ?? ''); ?></h5>
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </div>
                <div class="modal-body">
                    <iframe id="youtube-video" width="100%" height="315" src="" frameborder="0"
                        allowfullscreen></iframe>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <div class="col">
            <div class="card">
                <!-- Card header -->
                <div class="card-header border-0">
                    <h3 class="mb-0"><?php echo e(__('Reports')); ?></h3>
                    <form class="card-header-form">
                        <div class="input-group">
                            <input type="text" class="form-control" style="width: 200px" name="daterange" id="daterange"
                                value="<?php echo e($initialDateRange); ?>">
                            <select class="form-control" id="campaign_device" name="campaign_device" style="width: 200px">
                                <option value=""><?php echo e(__('Select Device')); ?></option>
                                <?php $__currentLoopData = $device_data; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $device): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <option value="<?php echo e($device['id']); ?>"><?php echo e($device['name']); ?>

                                        <?php if(!empty($device['phone'])): ?>
                                            (+<?php echo e($device['phone']); ?>)
                                        <?php endif; ?>
                                    </option>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </select>
                            <select class="form-control" id="camp_name" name="camp_name" style="width: 200px">
                                <option value=""><?php echo e(__('Select Campaign')); ?></option>
                                <?php $__currentLoopData = $camp_data; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $camp): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <?php if($camp): ?>
                                        <option value="<?php echo e($camp); ?>"><?php echo e($camp); ?></option>
                                    <?php endif; ?>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </select>
                            <button onclick="export_data(event)" class="btn btn-sm text-primary submit-button"><i
                                    class="fa fi-rs-file-excel" style="font-size: 2.5em;"></i></button>
                        </div>
                    </form>
                </div>
                <!-- Light table -->
                <div class="table-responsive">
                    <table id="example" class="table table-bordered table-striped table-hover dataTable">
                        <thead>
                            <tr>
                                <th>Receiver</th>
                                <th>Status</th>
                                <th>Template</th>
                                <th>Campaign Name</th>
                                <th>Send Time</th>
                                <th>Delivered Time</th>
                                <th>Read Time</th>
                                <th>Account Name</th>
                                
                            </tr>
                        </thead>
                        <tfoot>
                            <tr>
                                <th>Receiver</th>
                                <th>Status</th>
                                <th>Template</th>
                                <th>Campaign Name</th>
                                <th>Send Time</th>
                                <th>Delivered Time</th>
                                <th>Read Time</th>
                                <th>Account Name</th>
                                
                            </tr>
                        </tfoot>
                    </table>
                </div>
                <div class="card-footer py-4">
                    
                </div>
            </div>
        </div>
    </div>
<?php $__env->stopSection(); ?>
<?php $__env->startPush('js'); ?>
    <script>
        // $('#test').hover(
        //     function() {
        //         $('.todayTotal').show(); // Show the today-pending div on hover
        //     },
        //     function() {
        //         $('.todayTotal').hide(); // Hide it again when hover ends
        //     }
        // );
        const tutorial = <?php echo json_encode($videoTutorial, 15, 512) ?>;

        // Function to extract video ID from YouTube URL
        function getVideoId(url) {
            const regex =
                /(?:https?:\/\/)?(?:www\.)?(?:youtu\.be\/|(?:www\.)?youtube\.com\/(?:watch\?v=|embed\/|v\/|.+\?v=))([^&]+)/;
            const matches = url.match(regex);
            return matches ? matches[1] : null;
        }

        // Event listener for the modal opening
        $('#videoModal').on('show.bs.modal', function() {
            const videoId = getVideoId(tutorial.value); // Extract video ID from the tutorial URL
            if (videoId) {
                const videoUrl = `https://www.youtube.com/embed/${videoId}`;

                // Set the video URL to the iframe
                document.getElementById('youtube-video').src = videoUrl;
            } else {
                console.error('Invalid YouTube URL');
            }
        });

        // Clear the video source when the modal is hidden
        $('#videoModal').on('hidden.bs.modal', function() {
            document.getElementById('youtube-video').src = '';
        });
    </script>
<?php $__env->stopPush(); ?>
<?php $__env->startPush('js'); ?>
    <script type="text/javascript" src="https://code.jquery.com/jquery-3.7.0.js"></script>
    <script type="text/javascript" src="https://cdn.datatables.net/1.13.6/js/jquery.dataTables.min.js"></script>
    <script type="text/javascript" src="https://cdn.datatables.net/1.13.6/js/dataTables.bootstrap5.min.js"></script>
    <script type="text/javascript" src="https://cdn.jsdelivr.net/momentjs/latest/moment.min.js"></script>

    <script type="text/javascript" src="https://cdn.jsdelivr.net/npm/daterangepicker/daterangepicker.min.js"></script>
    <link rel="stylesheet" type="text/css" href="https://cdn.jsdelivr.net/npm/daterangepicker/daterangepicker.css" />
    <link rel="stylesheet" type="text/css" href="https://cdn.datatables.net/1.13.6/css/dataTables.bootstrap5.min.css" />

    <script src="<?php echo e(asset('assets/js/pages/user/report-index.js')); ?>"></script>

    <script type="text/javascript">
        var start_date = moment().format('YYYY-MM-DD');
        var end_date = moment().format('YYYY-MM-DD');
        var select_device = '';
        var select_campaign = '';

        // date range filter starts
        $(document).ready(function() {
            $(function() {
                var initialDateRange = start_date + ' - ' + end_date;

                $('input[name="daterange"]').daterangepicker({
                    opens: 'left',
                }, function(start, end, label) {
                    start_date = start.format('YYYY-MM-DD');
                    end_date = end.format('YYYY-MM-DD');
                    $('#example').DataTable().ajax.reload(null, true);
                });

                // Set the initial date range text
                $('input[name="daterange"]').val(initialDateRange);

                // Initialize DataTable with the initial date range
                $('#example').DataTable().ajax.reload(null, true);
            });

            $(document).ready(function() {
                // Check the number of options in the select element
                var $deviceSelect = $('#campaign_device');
                var deviceOptions = $deviceSelect.find('option').not(
                    ':first'); // Exclude the "Select Device" option

                if (deviceOptions.length === 1) {
                    // Automatically select the only available device
                    $deviceSelect.val(deviceOptions.val()).change();
                }

                // Attach change event handler
                $deviceSelect.change(function() {
                    select_device = $(this).val();
                    $('#example').DataTable().ajax.reload(null, true);
                });
            });
            $('#camp_name').change(function() {
                select_campaign = $(this).val();
                $('#example').DataTable().ajax.reload(null, true);
            });
        });


        //data range filter ends

        function export_data(event) {
            event.preventDefault();
            // console.log(start_date);
            var downloadUrl = "<?php echo e(route('user.export_data')); ?>?start_date=" + start_date + "&end_date=" + end_date +
                "&select_device=" + select_device + "&select_campaign=" + select_campaign;
            window.location.href = downloadUrl;
        }

        $(document).ready(function() {


            $('#example').DataTable({
                dom: 'lfrtp',
                deferRender: true,
                searchable: true,
                processing: true,
                orderClasses: true,
                serverSide: true,
                language: {
                    paginate: {
                        next: '&#8594;', // or '→'
                        previous: '&#8592;' // or '←' 
                    }
                },
                order: [
                    [4, "desc"]
                ],
                ajax: {
                    url: '<?php echo e(route('user.get_data_filter')); ?>',
                    type: 'POST',
                    dataType: 'JSON',
                    headers: {
                        'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
                    },
                    data: function(d) {
                        d.start_date = start_date;
                        d.end_date = end_date;
                        d.select_device = select_device;
                        d.select_campaign = select_campaign;
                    }
                },
                columns: [{
                        data: 'send_to_number'
                    },
                    {
                        data: 'status_text',
                        //add tooltip for status
                        render: function(data, type, row, meta) {
                            if (data === 'Pending') {
                                return "<span class='badge badge-pill badge-warning' data-toggle='tooltip' data-placement='top' title='" +
                                    (row.task_description ? row.task_description : '') + "'>" +
                                    data + "</span>";
                            } else if (data === 'sent') {
                                return "<span class='badge badge-pill badge-primary' data-toggle='tooltip' data-placement='top' title='" +
                                    (row.task_description ? row.task_description : '') + "'>" +
                                    data + "</span>";
                            } else if (data === 'delivered') {
                                return "<span class='badge badge-pill badge-info' data-toggle='tooltip' data-placement='top' title='" +
                                    (row.task_description ? row.task_description : '') + "'>" +
                                    data + "</span>";
                            } else if (data === 'Read') {
                                return "<span class='badge badge-pill badge-success' data-toggle='tooltip' data-placement='top' title='" +
                                    (row.task_description ? row.task_description : '') + "'>" +
                                    data + "</span>";
                            } else if (data === 'failed') {
                                return "<span class='badge badge-pill badge-danger' data-toggle='tooltip' data-placement='top' title='" +
                                    (row.task_description ? row.task_description : '') + "'>" +
                                    data + "</span>";
                            } else {
                                return "<span class='badge badge-pill badge-secondary' data-toggle='tooltip' data-placement='top' title='" +
                                    (row.task_description ? row.task_description : '') + "'>" +
                                    data + "</span>";
                            }
                        }
                    },
                    {
                        data: 'template_name'
                    },
                    {
                        // data: 'whatsapp_sent_time',
                        // //if whatsapp_sent_time is null then use created_at
                        // render: function(data, type, row, meta) {
                        //     if (data === null) {
                        //         data = row.launched_on;
                        //     }
                        //     return data;
                        // },
                        // type: 'date'
                        data: 'campaign_name'
                    },
                    {
                        data: 'scheduled_on'
                    },
                    {
                        data: 'whatsapp_received_time'
                    },
                    {
                        data: 'whatsapp_read_time'
                    },
                    {
                        data: 'name',
                        //add mobile number in name
                        render: function(data, type, row, meta) {
                            return data + '<br>' + '('+ row.phone+')';
                        }
                    },
                    
                    
                    
                    // {
                    //     data: 'status_text'
                    // },
                    // {
                    //     data: 'task_url',
                    //     render: function(data, type, row, meta) {
                    //         if (row.task_type === 'TEXT') {
                    //             return 'No Media';
                    //         } else {
                    //             if (data === null) {
                    //                 return 'No Media';
                    //             }
                    //             return "<a href='" + data +
                    //                 "' target='_blank' rel='noopener noreferrer' class='btn btn-sm btn-outline-primary submit-button waves-float'><i class='fa fa-eye'></i></a>";
                    //         }
                    //     }
                    // }
                ],
            });
        });
    </script>
<?php $__env->stopPush(); ?>

<?php echo $__env->make('layouts.main.app', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\Users\<USER>\OneDrive\Vishal\xampp\htdocs\waba_panelv2\resources\views/user/report/index.blade.php ENDPATH**/ ?>