
<?php $__env->startSection('head'); ?>
    <?php echo $__env->make('layouts.main.headersection', [
        'title' => __('Greetings'),
        'buttons' => isset($videoTutorial->value)
            ? [
                [
                    'name' => '<i class="fi-rs-photo-video"></i>&nbsp;&nbsp;' . __('Video Tutorial'),
                    'url' => '#',
                    'components' => 'data-toggle="modal" data-target="#videoModal"',
                    'is_button' => true,
                ],
            ]
            : [],
    ], \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
<?php $__env->stopSection(); ?>
<?php $__env->startPush('topcss'); ?>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/uikit@3.9.4/dist/css/uikit.min.css">
    <link rel="stylesheet" type="text/css" href="<?php echo e(asset('assets/vendor/select2/dist/css/select2.min.css')); ?>">
<?php $__env->stopPush(); ?>
<?php $__env->startSection('content'); ?>
    <div class="row justify-content-center">
        <div class="col-12">
            <?php if(api_plan() && api_plan()->title == 'Api'): ?>
                <div class="row">
                    <div class="col-sm-12">
                        <div class="alert bg-gradient-primary text-white alert-dismissible fade show" role="alert">
                            <span class="alert-icon"><i class="fi  fi-rs-info text-white"></i></span>
                            <span class="alert-text">
                                <strong><?php echo e(__('!Opps ')); ?></strong>

                                <?php echo e(__('Greeting features is not available in your subscription plan')); ?>


                            </span>
                        </div>
                    </div>
                </div>
            <?php endif; ?>
            <div class="card">
                <div class="card-header row">
                    <h4 class="text-left col-6"><?php echo e(__('Greetings ')); ?>

                        <small class="text-dark"><?php echo e(__('(Messages will be sent at 9 AM)')); ?></small>
                    </h4>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-12 col-sm-12 col-md-12">
                            <div class="tab-content no-padding" id="myTab2Content">
                                <div class="tab-pane fade show active" id="mode_1" role="tabpanel"
                                    aria-labelledby="home-tab4">
                                    <form method="POST" action="<?php echo e(route('user.greetings.store')); ?>"
                                        class="ajaxform_reset_form" enctype="multipart/form-data">
                                        <?php echo csrf_field(); ?>
                                        <div class="row">
                                            <div class="col-sm-12  mb-4">
                                                <label><?php echo e(__('Select Device')); ?></label>
                                                <select class="form-control" name="b_device" id="b_device"
                                                    data-toggle="select">
                                                    <option value="-1"><?php echo e(__('-- Select Device --')); ?></option>
                                                    <?php $__currentLoopData = $devices; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $device): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                        <option value="<?php echo e($device['id']); ?>"><?php echo e($device['name']); ?>

                                                            (+<?php echo e($device['phone']); ?>)
                                                        </option>
                                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                                </select>
                                            </div>
                                            <div class="col-sm-12  mb-4">
                                                <label><?php echo e(__('Select Birthday Template')); ?></label>
                                                <select class="form-control" name="b_temp" id="b_temp"
                                                    data-toggle="select">
                                                </select>
                                            </div>
                                            <div class="col-sm-12  mb-4">
                                                <label><?php echo e(__('Select Anniversary Template')); ?></label>
                                                <select class="form-control" name="a_temp" id="a_temp"
                                                    data-toggle="select">
                                                </select>
                                            </div>
                                            <div class="col-sm-12">
                                                <div class="col-sm-12">
                                                    <button type="submit"
                                                        class="btn btn-outline-primary submit-button float-right"><?php echo e(__('Save Greetings')); ?></button>
                                                </div>
                                            </div>
                                        </div>
                                    </form>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Modal for YouTube video -->
    <div class="modal fade" id="videoModal" tabindex="-1" role="dialog" aria-labelledby="videoModalLabel"
        aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="videoModalLabel"><?php echo e($videoTutorial->title ?? ''); ?></h5>
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </div>
                <div class="modal-body">
                    <iframe id="youtube-video" width="100%" height="315" src="" frameborder="0"
                        allowfullscreen></iframe>
                </div>
            </div>
        </div>
    </div>

    </div>
    </div>
<?php $__env->stopSection(); ?>
<?php $__env->startPush('js'); ?>
    <script src="<?php echo e(asset('assets/vendor/select2/dist/js/select2.min.js')); ?>"></script>
    <script src="https://woody180.github.io/vanilla-javascript-emoji-picker/vanillaEmojiPicker.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/uikit@3.9.4/dist/js/uikit.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/uikit@3.9.4/dist/js/uikit-icons.min.js"></script>

    <script type="text/javascript" src="<?php echo e(asset('assets/js/pages/bulk/template.js')); ?>"></script>

    <script>
        $(document).ready(function() {
            document.getElementById('b_temp').innerText = null;
            document.getElementById('a_temp').innerText = null;

            $(document).ready(function() {
                function selectSingleDevice() {
                    var deviceDropdown = $("#b_device");
                    if (deviceDropdown.children("option").length == 2) { // One option is the placeholder
                        deviceDropdown.children("option").eq(1).prop('selected', true);
                        deviceDropdown.change();
                    }
                }
                selectSingleDevice();
            });

            $('#b_device').on('change', function() {
                var selectedDevice = $(this).val();
                $('#template_select').empty().append(
                    '<option value="" >-- Select Template --</option>');

                if (!selectedDevice) return;

                $.ajax({
                    url: "<?php echo e(route('user.get_templates', ':device_id')); ?>".replace(':device_id',
                        selectedDevice),
                    type: 'GET',
                    success: function(templates) {
                        var optionData = '<option value="-1">-- Select Template --</option>';
                        $.each(templates, function(index, value) {
                            if (value.status == "1") {
                                optionData += "<option value=" + value.id + ">" + value
                                    .title + " -- " +
                                    value.type + "</option>";
                            }
                        });
                        $('#a_temp').append(optionData);
                        $('#b_temp').append(optionData);

                        var greetings = <?php echo json_encode($greetings, 15, 512) ?>;
                        if (greetings) {
                            $('#b_temp').val(greetings.birth_day_temp);
                            $('#a_temp').val(greetings.anni_day_temp);
                        }
                    },
                    error: function(xhr) {
                        console.error('AJAX error:', xhr.responseText);
                    }
                });
            });
        });
    </script>
    <script>
        const tutorial = <?php echo json_encode($videoTutorial, 15, 512) ?>;

        // Function to extract video ID from YouTube URL
        function getVideoId(url) {
            const regex =
                /(?:https?:\/\/)?(?:www\.)?(?:youtu\.be\/|(?:www\.)?youtube\.com\/(?:watch\?v=|embed\/|v\/|.+\?v=))([^&]+)/;
            const matches = url.match(regex);
            return matches ? matches[1] : null;
        }

        // Event listener for the modal opening
        $('#videoModal').on('show.bs.modal', function() {
            const videoId = getVideoId(tutorial.value); // Extract video ID from the tutorial URL
            if (videoId) {
                const videoUrl = `https://www.youtube.com/embed/${videoId}`;

                // Set the video URL to the iframe
                document.getElementById('youtube-video').src = videoUrl;
            } else {
                console.error('Invalid YouTube URL');
            }
        });

        // Clear the video source when the modal is hidden
        $('#videoModal').on('hidden.bs.modal', function() {
            document.getElementById('youtube-video').src = '';
        });
    </script>
<?php $__env->stopPush(); ?>

<?php echo $__env->make('layouts.main.app', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\Users\<USER>\OneDrive\Vishal\xampp\htdocs\waba_panelv2\resources\views/user/greetings/create.blade.php ENDPATH**/ ?>