
<?php $__env->startSection('head'); ?>
    <?php echo $__env->make('layouts.main.headersection', [
        'title' => 'All Templates',
        'buttons' => isset($videoTutorial->value)
            ? [
                [
                    'name' => '<i class="fi-rs-photo-video"></i>&nbsp;&nbsp;' . __('Video Tutorial'),
                    'url' => '#',
                    'components' => 'data-toggle="modal" data-target="#videoModal"',
                    'is_button' => true,
                ],
            ]
            : [],
    ], \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
<?php $__env->stopSection(); ?>
<?php $__env->startSection('content'); ?>
    <div class="row justify-content-center">
        <div class="col-12">
            <?php if(count($devices ?? []) > 0): ?>
                <div class="row">
                    <?php $__currentLoopData = $devices ?? []; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $device): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <div class="col-xl-4 col-md-6">
                            <div class="card  border-0">
                                <!-- Card body -->
                                <div class="card-body">
                                    <div class="row">
                                        <div class="col">
                                            <h5 class="card-title text-uppercase text-muted mb-0 text-dark">
                                                <?php echo e($device['name']); ?>

                                                <span
                                                    class="badge badge-<?php echo e($device['status'] == '1' ? 'success' : 'danger'); ?>">
                                                    <?php echo e($device['status'] == '1' ? 'Active' : 'Deactive'); ?>

                                                </span>
                                            </h5>

                                            <div class="mt-3 mb-0">
                                                
                                                
                                                <span class="pt-2 text-dark"><?php echo e(__('Total Templates:')); ?>

                                                    <?php echo e(number_format($total_templates)); ?> </span>

                                            </div>
                                        </div>
                                    </div>
                                    <div class="text-center">
                                        <a href="<?php echo e(route('user.template.viewtemplate', ['uuid' => $device['uuid']])); ?>">
                                            <button
                                                class="btn btn-outline-primary submit-button btn-sm float-right"><?php echo e(__('View Template')); ?>

                                            </button>
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </div>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                </div>
            <?php else: ?>
                <div class="alert  bg-gradient-primary text-white"><span
                        class="text-left"><?php echo e(__('Opps There Is No Template Found....')); ?></span></div>
            <?php endif; ?>
        </div>
    </div>

    <!-- Modal for YouTube video -->
    <div class="modal fade" id="videoModal" tabindex="-1" role="dialog" aria-labelledby="videoModalLabel"
        aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="videoModalLabel"><?php echo e($videoTutorial->title ?? ''); ?></h5>
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </div>
                <div class="modal-body">
                    <iframe id="youtube-video" width="100%" height="315" src="" frameborder="0"
                        allowfullscreen></iframe>
                </div>
            </div>
        </div>
    </div>

<?php $__env->stopSection(); ?>
<?php $__env->startPush('js'); ?>
    <script src="<?php echo e(asset('assets/js/pages/user/template-index.js')); ?>"></script>

    <script>
        const tutorial = <?php echo json_encode($videoTutorial, 15, 512) ?>;

        // Function to extract video ID from YouTube URL
        function getVideoId(url) {
            const regex =
                /(?:https?:\/\/)?(?:www\.)?(?:youtu\.be\/|(?:www\.)?youtube\.com\/(?:watch\?v=|embed\/|v\/|.+\?v=))([^&]+)/;
            const matches = url.match(regex);
            return matches ? matches[1] : null;
        }

        // Event listener for the modal opening
        $('#videoModal').on('show.bs.modal', function() {
            const videoId = getVideoId(tutorial.value); // Extract video ID from the tutorial URL
            if (videoId) {
                const videoUrl = `https://www.youtube.com/embed/${videoId}`;

                // Set the video URL to the iframe
                document.getElementById('youtube-video').src = videoUrl;
            } else {
                console.error('Invalid YouTube URL');
            }
        });

        // Clear the video source when the modal is hidden
        $('#videoModal').on('hidden.bs.modal', function() {
            document.getElementById('youtube-video').src = '';
        });
    </script>
<?php $__env->stopPush(); ?>

<?php echo $__env->make('layouts.main.app', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\Users\<USER>\OneDrive\Vishal\xampp\htdocs\waba_panelv2\resources\views/user/template/index.blade.php ENDPATH**/ ?>