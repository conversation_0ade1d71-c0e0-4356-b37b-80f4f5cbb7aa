
<?php $__env->startSection('head'); ?>
    <?php echo $__env->make('layouts.main.headersection', [
        'title' => __('Agents'),
        'buttons' => [
            [
                'name' => '<i class="fa fa-plus"></i>&nbsp' . __('Create a Agent'),
                'url' => route('user.agent.create'),
            ],
        ],
    ], \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
<?php $__env->stopSection(); ?>
<?php $__env->startSection('content'); ?>
    <div class="row">
        <?php if(Session::has('error')): ?>
            <div class="col-sm-12">
                <div class="alert bg-gradient-danger text-white alert-dismissible fade show success-alert" id="success-alert"
                    role="alert">
                    <span class="alert-text"><?php echo e(Session::get('error')); ?></span>
                    <button type="button" class="close" data-dismiss="alert" aria-label="Close">
                        <span aria-hidden="true">×</span>
                    </button>
                </div>
            </div>
        <?php endif; ?>

        <?php if(getUserPlanData('access_agent_list') == true): ?>
            <div class="col-sm-12">
                <div class="card">
                    <div class="card-body">
                        <div class="row mb-30">
                            <div class="col-lg-6">
                                <h4><?php echo e(__('Agents')); ?></h4>
                            </div>
                            <div class="col-lg-6">
                            </div>
                        </div>
                        <br>
                        <div class="card-action-filter">
                            <div class="table-responsive custom-table">
                                <table class="table">
                                    <thead>
                                        <tr>
                                            <th><?php echo e(__('Name')); ?></th>
                                            <th><?php echo e(__('Email')); ?></th>
                                            <th><?php echo e(__('Status')); ?></th>
                                            
                                            
                                            <th class="text-right"><?php echo e(__('Action')); ?></th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php $__currentLoopData = $users; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $row): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                            <tr>
                                                <td>
                                                    <?php echo e($row->name); ?>

                                                </td>
                                                <td>
                                                    <?php echo e($row->email); ?>

                                                </td>
                                                <td>
                                                    <?php if($row->status == 1): ?>
                                                        <span class="badge badge-success"><?php echo e(__('Active')); ?></span>
                                                    <?php else: ?>
                                                        <span class="badge badge-danger"><?php echo e(__('Deactive')); ?></span>
                                                    <?php endif; ?>
                                                </td>
                                                
                                                
                                                <td class="text-right">
                                                    <a href="<?php echo e(route('user.agent.edit', $row->id)); ?>"
                                                        class="btn btn-neutral btn-sm"><?php echo e(__('Edit')); ?></a>
                                                    <a href="#"
                                                        data-action="<?php echo e(route('user.agent.destroy', $row->id)); ?>"
                                                        class="btn btn-neutral btn-sm delete-confirm"><?php echo e(__('Delete')); ?></a>
                                                </td>
                                            </tr>
                                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        <?php else: ?>
            <div class="col-sm-12">
                <div class="alert bg-gradient-primary text-white alert-dismissible fade show" role="alert">
                    <span class="alert-icon"><i class="fi  fi-rs-info text-white"></i></span>
                    <span class="alert-text">
                        <strong><?php echo e(__('!Opps ')); ?></strong>
                        <?php echo e(__('Agent access features is not available in your subscription plan')); ?>

                    </span>
                </div>
            </div>
        <?php endif; ?>
    </div>
<?php $__env->stopSection(); ?>
<?php $__env->startPush('js'); ?>
<script>
	setTimeout(function() {
		document.getElementById('success-alert')?.remove();
		document.getElementById('error-alert')?.remove();
	}, 10000);
</script>
<?php $__env->stopPush(); ?>
<?php echo $__env->make('layouts.main.app', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\Users\<USER>\OneDrive\Vishal\xampp\htdocs\waba_panelv2\resources\views/user/agent/index.blade.php ENDPATH**/ ?>