
<?php $__env->startSection('head'); ?>
    <?php echo $__env->make('layouts.main.headersection', [
        'title' => 'Tags',
        'buttons' => array_filter([
            isset($videoTutorial->value)
                ? [
                    'name' => '<i class="fi-rs-photo-video"></i>&nbsp;&nbsp;' . __('Video Tutorial'),
                    'url' => '#',
                    'components' => 'data-toggle="modal" data-target="#videoModal"',
                    'is_button' => true,
                ]
                : null,
            [
                'name' => '<i class="fas fa-plus"></i>&nbsp;Create Tag',
                'url' => '#',
                'components' => 'data-toggle="modal" data-target="#create-new-tag" id="create-new-tags"',
                'is_button' => true,
            ],
        ]),
    ], \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
<?php $__env->stopSection(); ?>
<?php $__env->startPush('topcss'); ?>
    <link rel="stylesheet" type="text/css" href="<?php echo e(asset('assets/vendor/select2/dist/css/select2.min.css')); ?>">
<?php $__env->stopPush(); ?>
<?php $__env->startSection('content'); ?>
    <div class="row justify-content-center">
        <div class="col-12">
            <?php if(api_plan() && api_plan()->title == 'Api'): ?>
                <div class="row">
                    <div class="col-sm-12">
                        <div class="alert bg-gradient-primary text-white alert-dismissible fade show" role="alert">
                            <span class="alert-icon"><i class="fi  fi-rs-info text-white"></i></span>
                            <span class="alert-text">
                                <strong><?php echo e(__('!Opps ')); ?></strong>

                                <?php echo e(__('Tag features is not available in your subscription plan')); ?>


                            </span>
                        </div>
                    </div>
                </div>
            <?php endif; ?>
            <div class="card">
                <div class="card-header border-0">
                    <h3 class="mb-0"><?php echo e(__('Tag List')); ?></h3>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-sm-12 table-responsive">
                            <table class="table col-12">
                                <thead>
                                    <tr>
                                        <th><?php echo e(__('Tag Name')); ?></th>
                                        <th><?php echo e(__('Keyword')); ?></th>
                                        <th><?php echo e(__('Color Code')); ?></th>
                                        <th class="text-right"><?php echo e(__('Action')); ?></th>
                                    </tr>
                                </thead>
                                <tbody class="tbody">
                                    <?php $__currentLoopData = $tags ?? []; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $tag): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                        <?php if($tag): ?>
                                            <tr>
                                                <td><?php echo e($tag->tag_name ?? 'N/A'); ?></td>
                                                <td><?php echo e($tag->keyword ?? 'N/A'); ?></td>
                                                
                                                <td>
                                                    <?php if($tag->color_code): ?>
                                                        <svg height="20" width="60">
                                                            <rect width="100%" height="100%"
                                                                fill="<?php echo e($tag->color_code); ?>"></rect>
                                                        </svg>
                                                    <?php else: ?>
                                                        N/A
                                                    <?php endif; ?>
                                                </td>
                                                <td>
                                                    <div class="btn-group mb-2 float-right">
                                                        <button class="btn btn-neutral btn-sm dropdown-toggle"
                                                            type="button" data-toggle="dropdown" aria-haspopup="true"
                                                            aria-expanded="false">
                                                            <?php echo e(__('Action')); ?>

                                                        </button>
                                                        <div class="dropdown-menu">
                                                            <a class="dropdown-item has-icon edit-reply" href="#"
                                                                data-action="<?php echo e(route('user.tag.update', $tag->id)); ?>"
                                                                data-tag_name="<?php echo e($tag->tag_name); ?>"
                                                                data-keyword="<?php echo e($tag->keyword); ?>"
                                                                data-color_code="<?php echo e($tag->color_code); ?>"
                                                                data-toggle="modal" data-target="#editModal"
                                                                data-tag-id="<?php echo e($tag->id); ?>"
                                                                data-tag-name="<?php echo e($tag->tag_name); ?>"
                                                                data-color-code="<?php echo e($tag->color_code); ?>"
                                                                data-drip-id="<?php echo e($tag->drip_id); ?>"
                                                                data-keyword="<?php echo e($tag->keyword); ?>">
                                                                <i class="ni ni-align-left-2"></i><?php echo e(__('Edit')); ?></a>
                                                            <a class="dropdown-item has-icon delete-confirm"
                                                                href="javascript:void(0)"
                                                                data-action="<?php echo e(route('user.tag.destroy', $tag->id)); ?>"><i
                                                                    class="fas fa-trash"></i><?php echo e(__('Remove Tag')); ?></a>
                                                        </div>
                                                    </div>
                                                </td>
                                            </tr>
                                        <?php else: ?>
                                            <tr>
                                                <td colspan="3">Tag Not Found</td>
                                            </tr>
                                        <?php endif; ?>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                </tbody>
                            </table>
                            <div class="d-flex justify-content-center">
                                <?php echo e($tags->links('vendor.pagination.bootstrap-4')); ?>

                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>


    <!-- Modal for YouTube video -->
    <div class="modal fade" id="videoModal" tabindex="-1" role="dialog" aria-labelledby="videoModalLabel"
        aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="videoModalLabel"><?php echo e($videoTutorial->title ?? ''); ?></h5>
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </div>
                <div class="modal-body">
                    <iframe id="youtube-video" width="100%" height="315" src="" frameborder="0"
                        allowfullscreen></iframe>
                </div>
            </div>
        </div>
    </div>

    <div class="modal fade" id="create-new-tag" tabindex="-1" aria-labelledby="editModal" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <form type="POST" action="<?php echo e(route('user.create_tag')); ?>" class="ajaxform_instant_reload">
                    <?php echo csrf_field(); ?>
                    <div class="modal-header">
                        <h5 class="modal-title" id="exampleModalLabel"><?php echo e(__('Create Tag')); ?></h5>
                        <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                            <span aria-hidden="true">&times;</span>
                        </button>
                    </div>
                    <div class="modal-body">
                        <div class="form-group">
                            <label><?php echo e(__('Tag Name')); ?></label>
                            <input type="text" name="tag_name" class="form-control" id="tag_name" required="">
                        </div>
                        <div class="form-group text-area-edit" id="reply-area-edit">
                            <label><?php echo e(__('Keyword to auto assign tag')); ?></label>
                            <input type="text" class="form-control" name="autoassign_key" id="autoassign_key">
                        </div>
                        <div class="form-group">
                            <label><?php echo e(__('Drip')); ?></label>
                            <select class="form-control" id="drip_id" name="drip_id" data-toggle="select">
                                <option value=""><?php echo e(__('-- Select Drip --')); ?></option>
                                <?php $__currentLoopData = $drips; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $drip): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <option value="<?php echo e($drip['id']); ?>"><?php echo e($drip['drip_name']); ?>

                                    </option>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </select>
                        </div>
                        <div class="form-group text-area-edit" id="reply-area-edit">
                            <label><?php echo e(__('Color Code')); ?></label>
                            <input type="color" name="color_code" width="10px" class="form-control"
                                id="color_code">
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="submit"
                            class="btn btn-neutral submit-btn float-right"><?php echo e(__('Create Now')); ?></button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <div class="modal fade" id="editModal" tabindex="-1" aria-labelledby="editModal" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <form type="POST" action="" class="ajaxform_instant_reload edit-reply-form">
                    <?php echo csrf_field(); ?>
                    <?php echo method_field('PUT'); ?>
                    <div class="modal-header">
                        <h5 class="modal-title" id="exampleModalLabel"><?php echo e(__('Edit Tag')); ?></h5>
                        <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                            <span aria-hidden="true">&times;</span>
                        </button>
                    </div>
                    <div class="modal-body">
                        <div class="form-group">
                            <label><?php echo e(__('Tag Name')); ?></label>
                            <input type="text" name="edit_tag_name" class="form-control" id="edit_tag_name"
                                required="">
                        </div>
                        <div class="form-group text-area-edit" id="reply-area-edit">
                            <label><?php echo e(__('Keyword to auto assign tag')); ?></label>
                            <input type="text" class="form-control" name="edit_autoassign_key"
                                id="edit_autoassign_key">
                        </div>
                        <div class="form-group">
                            <label><?php echo e(__('Drip')); ?></label>
                            <select class="form-control" id="edit_drip_id" name="edit_drip_id" required data-toggle="select">
                                <option value=""><?php echo e(__('-- Select Drip --')); ?></option>
                                <?php $__currentLoopData = $drips; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $drip): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <option value="<?php echo e($drip['id']); ?>"><?php echo e($drip['drip_name']); ?>

                                    </option>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </select>
                        </div>
                        <div class="form-group text-area-edit" id="reply-area-edit">
                            <label><?php echo e(__('Color Code')); ?></label>
                            <input type="color" name="edit_color_code" width="10px" class="form-control"
                                id="edit_color_code">
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="submit"
                            class="btn btn-neutral submit-btn float-right"><?php echo e(__('Update Tag')); ?></button>
                    </div>
                </form>
            </div>
        </div>
    </div>
<?php $__env->stopSection(); ?>
<?php $__env->startPush('js'); ?>
    <script src="<?php echo e(asset('assets/vendor/select2/dist/js/select2.min.js')); ?>"></script>
    <script src="<?php echo e(asset('assets/js/pages/user/chatbot.js')); ?>"></script>

    <script>
        $(document).ready(function() {
            $('.edit-reply').on('click', function() {
                var tagId = $(this).data('tag-id');
                var tagName = $(this).data('tag-name');
                var colorCode = $(this).data('color-code');
                var keyword = $(this).data('keyword');
                var dripId =$(this).data('drip-id');

                // Set the values in the modal
                $('#editModal').find('#edit_tag_name').val(tagName);
                $('#editModal').find('#edit_autoassign_key').val(keyword);
                $('#editModal').find('#edit_color_code').val(colorCode);
                $('#editModal').find('#edit_drip_id').val(dripId).trigger('change');

                // Update the form action with the tag ID
                var actionUrl = "<?php echo e(route('user.tag.update', ':id')); ?>".replace(':id', tagId);
                $('#editModal').find('form').attr('action', actionUrl);
            });
        });
    </script>
    <script>
        const tutorial = <?php echo json_encode($videoTutorial, 15, 512) ?>;

        // Function to extract video ID from YouTube URL
        function getVideoId(url) {
            const regex =
                /(?:https?:\/\/)?(?:www\.)?(?:youtu\.be\/|(?:www\.)?youtube\.com\/(?:watch\?v=|embed\/|v\/|.+\?v=))([^&]+)/;
            const matches = url.match(regex);
            return matches ? matches[1] : null;
        }

        // Event listener for the modal opening
        $('#videoModal').on('show.bs.modal', function() {
            const videoId = getVideoId(tutorial.value); // Extract video ID from the tutorial URL
            if (videoId) {
                const videoUrl = `https://www.youtube.com/embed/${videoId}`;

                // Set the video URL to the iframe
                document.getElementById('youtube-video').src = videoUrl;
            } else {
                console.error('Invalid YouTube URL');
            }
        });

        // Clear the video source when the modal is hidden
        $('#videoModal').on('hidden.bs.modal', function() {
            document.getElementById('youtube-video').src = '';
        });
    </script>
<?php $__env->stopPush(); ?>

<?php echo $__env->make('layouts.main.app', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\Users\<USER>\OneDrive\Vishal\xampp\htdocs\waba_panelv2\resources\views/user/tag/index.blade.php ENDPATH**/ ?>