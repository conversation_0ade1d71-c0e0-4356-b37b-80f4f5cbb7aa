<?php

namespace App\Http\Controllers\Frontend;

use App\Http\Controllers\Controller;
use App\Models\Tenant;
use Illuminate\Http\Request;
use App\Models\Post;
use App\Models\Plan;
use App\Models\User;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Session;
use Str;
use App\Traits\Seo;

class PricingController extends Controller
{

    use Seo;

    /**
     * Display a pricing page of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index()
    {
        $faqs = Post::where('type', 'faq')->where('featured', 1)->where('lang', app()->getLocale())->with('excerpt')->latest()->get();
        $plans = Plan::where('status', 1)->latest()->get();

        $this->metadata('seo_pricing');

        return view('frontend.plans', compact('faqs', 'plans'));
    }

    /**
     * Display the specified resource.
     *
     * @param  string  $id
     * @param  Request 
     * @return \Illuminate\Http\Response
     */
    public function register(Request $request, $id)
    {
        $plan = Plan::where('status', 1)->findorFail($id);

        $meta['title'] = $plan->title ?? '';
        $this->pageMetaData($meta);


        return view('frontend.register', compact('plan', 'request'));
    }


    /**
     * register a user with plan
     *
     * @param  integer  $id
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function registerPlan(Request $request, $id)
    {
        $request->validate([
            'name' => ['required', 'string', 'max:255'],
            'email' => ['required', 'string', 'email', 'max:255', 'unique:users'],
            'phone' => ['required', 'string', 'min:10', 'unique:users'],
            'password' => ['required', 'string', 'min:8'],
        ]);

        $plan = Plan::where('status', 1)->findorFail($id);
        $domain = request()->getHost();
        $subdomain = explode('.', $domain)[0];

        $tenant = Tenant::where('domain', $domain)->orWhere('domain', $subdomain)->first();
        // dd($tenant);
        if ($tenant) {
            $reseller_id = $tenant->user_id; // Set the dynamic table name
        } else {
            $reseller_id = 1;
        }

        $user = new User;
        $user->name = $request->name;
        $user->email = $request->email;
        $user->phone = $request->phone;
        $user->reseller_id = $reseller_id;
        $user->role = 'user';
        $user->status = 1;
        $user->plan = json_encode($plan->data);
        $user->plan_id = $plan->id;
        $user->will_expire = $plan->is_trial == 1 ? now()->addDays($plan->trial_days) : null;
        $user->authkey = $this->generateAuthKey();
        $user->password = Hash::make($request->password);
        $user->save();

        Auth::login($user);

        if ($user->will_expire === null && request()->getHttpHost() === 'dmsv2.nxccontrols.in') {
            return redirect('user/subscription/' . $plan->id);
        } else {
            return redirect('user/dashboard');
        }

        Session::put('new-user', __('Lets create a whatsapp device'));
        return redirect('/user/device/create');
    }

    /**
     * generate auth key
     */
    public function generateAuthKey()
    {
        $rend = Str::random(50);
        $check = User::where('authkey', $rend)->first();

        if ($check == true) {
            $rend = $this->generateAuthKey();
        }
        return $rend;
    }
}
